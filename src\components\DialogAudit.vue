<template>
  <el-dialog
    title="商品审核"
    v-model="dialogVisible"
    width="50%"
  >
    <div v-if="product">
      <div class="product-info">
        <div class="product-image">
          <img :src="$filters.prefix(product.goodsCoverImg)" alt="商品图片" style="width: 200px; height: 200px;">
        </div>
        <div class="product-details">
          <p><strong>商品编号：</strong>{{ product.goodsId }}</p>
          <p><strong>商品名称：</strong>{{ product.goodsName }}</p>
          <p><strong>商品售价：</strong>{{ product.goodsSellingPrice }}</p>
          <p><strong>商品库存：</strong>{{ product.goodsStockNum }}</p>
          <p><strong>商户名称：</strong>{{ product.goodsMerchantName }}</p>
          <p><strong>供应商名称：</strong>{{ product.goodsSupplierName }}</p>
        </div>
      </div>
      
      <!-- <div class="audit-reason">
        <el-form :model="auditForm" label-width="100px">
          <el-form-item label="审核意见" v-if="auditForm.status === 2">
            <el-input
              type="textarea"
              v-model="auditForm.reason"
              placeholder="请输入审核不通过的原因"
              :rows="4"
            ></el-input>
          </el-form-item>
        </el-form>
      </div> -->
    </div>
    
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="success" @click="handleAuditSubmit(1)">审核通过</el-button>
        <el-button type="danger" @click="handleAuditSubmit(0)">审核不通过</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive } from 'vue'
import axios from '@/utils/axios'
import { ElMessage } from 'element-plus'

const dialogVisible = ref(false)
const product = ref(null)
const auditForm = reactive({
  status: 1, // 默认审核通过
  reason: ''
})

// 打开对话框
const open = (row) => {
  product.value = row
  dialogVisible.value = true
  auditForm.status = 1
  auditForm.reason = ''
}

// 提交审核结果
const handleAuditSubmit = (status) => {
  auditForm.status = status
  
//   // 如果是审核不通过，需要填写原因
//   if (status === 2 && !auditForm.reason) {
//     ElMessage.warning('请填写审核不通过的原因')
//     return
//   }
  
  axios.post('/goods/audit', {
    goodsId: product.value.goodsId,
    goodsAuditStatus: product.value.goodsAuditStatus,
    changeStatus: status,
  }).then(() => {
    ElMessage.success(status === 1 ? '审核通过成功' : '审核不通过成功')
    dialogVisible.value = false
    // 触发父组件刷新列表
    emit('reload')
  }).catch(err => {
    console.error('审核失败:', err)
    ElMessage.error('审核操作失败')
  })
}

// 向父组件暴露方法
defineExpose({
  open
})

// 定义事件
const emit = defineEmits(['reload'])
</script>

<style scoped>
.product-info {
  display: flex;
  margin-bottom: 20px;
}

.product-image {
  margin-right: 20px;
}

.product-details {
  flex: 1;
}

.audit-reason {
  margin-top: 20px;
}
</style>
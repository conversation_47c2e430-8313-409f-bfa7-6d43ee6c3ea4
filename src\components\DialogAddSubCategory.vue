<template>
  <el-dialog
    :title="state.type == 'add' ? '添加子级分类' : '修改子级分类'"
    v-model="state.visible"
    width="400px"
  >
    <el-form :model="state.ruleForm" :rules="state.rules" ref="formRef" label-width="100px" class="good-form">
      <el-form-item label="分类名称" prop="name" required>
        <el-input type="text" v-model="state.ruleForm.name" placeholder="请填写分类名称"></el-input>
      </el-form-item>
      <el-form-item label="分类图片" prop="img">
        <el-upload
          class="avatar-uploader"
          :action="state.uploadImgServer"
          accept="jpg,jpeg,png"
          :headers="{
            token: state.token
          }"
          :show-file-list="false"
          :before-upload="handleBeforeUpload"
          :on-success="handleImgSuccess"
        >
          <img style="width: 100px; height: 100px; border: 1px solid #e9e9e9;" v-if="state.ruleForm.img" :src="state.ruleForm.img" class="avatar">
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload>
        <div style="font-size: 12px; color: #999; margin-top: 5px;">建议尺寸: 180*180</div>
      </el-form-item>
      <el-form-item label="排序" prop="rank">
        <el-input type="number" v-model="state.ruleForm.rank" placeholder="可调整各分类之间的顺序"></el-input>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-switch v-model="state.ruleForm.status" :active-value="true" :inactive-value="false"></el-switch>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="state.visible = false">取消</el-button>
        <el-button type="primary" @click="submitForm">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { localGet, uploadImgServer } from '@/utils'
import { getCategoryDetail, saveCategories, updateCategories } from '@/api/category'

const props = defineProps({
  type: String, // 用于判断是添加还是编辑
  reload: Function, // 添加或修改完后，刷新列表页
  parentCategory: Object // 父级分类信息，用于添加子分类时
})

const formRef = ref(null)
const state = reactive({
  visible: false,
  categoryLevel: 2, // 默认为二级分类
  parentId: '',
  uploadImgServer,
  token: localGet('token') || '',
  type: 'add', // 默认为添加模式
  ruleForm: {
    name: '',
    rank: '',
    img: '',
    status: true // 默认为开启状态
  },
  rules: {
    name: [
      { required: true, message: '分类名称不能为空', trigger: ['blur', 'change'] }
    ]
  },
  id: '',
  parentCategory: null // 父级分类信息
})

// 图片上传前的验证
const handleBeforeUpload = (file) => {
  const sufix = file.name.split('.')[1] || ''
  if (!['jpg', 'jpeg', 'png'].includes(sufix)) {
    ElMessage.error('请上传 jpg、jpeg、png 格式的图片')
    return false
  }
}

// 图片上传成功的回调
const handleImgSuccess = (val) => {
  state.ruleForm.img = val.data || ''
}

// 获取分类详情
const getDetail = (id) => {
  getCategoryDetail(id).then(res => {
    state.ruleForm = {
      name: res.categoryName,
      rank: res.categoryRank || '',
      img: res.goodsCategoryImg || '',
      status: res.status !== false // 默认为开启状态
    }
    state.parentId = res.parentId
    state.categoryLevel = res.categoryLevel
  })
}

// 开启弹窗
const open = (id, type = 'add', parentCategory = null, categoryData = null) => {
  state.visible = true
  state.type = type

  if (parentCategory) {
    state.parentCategory = parentCategory
    state.parentId = parentCategory.categoryId
    state.categoryLevel = parentCategory.categoryLevel + 1
  }

  if (id) {
    state.id = id
    // 如果是有 id 传入，证明是修改模式
    if (categoryData) {
      // 如果传入了分类数据，直接使用
      state.ruleForm = {
        name: categoryData.categoryName,
        rank: categoryData.categoryRank || '',
        img: categoryData.goodsCategoryImg || '',
        status: categoryData.status !== false // 默认为开启状态
      }
      state.parentId = categoryData.parentId
      state.categoryLevel = categoryData.categoryLevel
    } else {
      // 否则通过API获取
      getDetail(id)
    }
  } else {
    // 重置表单
    state.ruleForm = {
      name: '',
      rank: '',
      img: '',
      status: true // 默认为开启状态
    }
  }
}

// 关闭弹窗
const close = () => {
  state.visible = false
}

// 提交表单
const submitForm = () => {
  formRef.value.validate((valid) => {
    if (valid) {
      // 构建提交的数据
      const data = {
        categoryLevel: state.categoryLevel,
        parentId: state.parentId,
        categoryName: state.ruleForm.name,
        categoryRank: state.ruleForm.rank || 0,
        categoryImg: state.ruleForm.img.url,
        categoryType: 0, // 子分类默认为实物类型
        status: state.ruleForm.status
      }

      if (state.type === 'add') {
        // 添加方法
        saveCategories(data).then(() => {
          ElMessage.success('添加成功')
          state.visible = false
          // 接口回调之后，运行重新获取列表方法 reload
          if (props.reload) props.reload()
        }).catch(err => {
          console.error('添加分类失败:', err)
          ElMessage.error('添加失败: ' + (err.message || '未知错误'))
        })
      } else {
        // 修改方法
        updateCategories({
          categoryId: state.id,
          ...data
        }).then(() => {
          ElMessage.success('修改成功')
          state.visible = false
          // 接口回调之后，运行重新获取列表方法 reload
          if (props.reload) props.reload()
        }).catch(err => {
          console.error('修改分类失败:', err)
          ElMessage.error('修改失败: ' + (err.message || '未知错误'))
        })
      }
    }
  })
}

defineExpose({ open, close })
</script>

<style scoped>
.avatar-uploader {
  width: 100px;
  height: 100px;
  color: #ddd;
  font-size: 30px;
}
.avatar-uploader :deep(.el-upload) {
  width: 100%;
  text-align: center;
}
.avatar-uploader-icon {
  display: block;
  width: 100%;
  height: 100%;
  border: 1px solid #e9e9e9;
  padding: 32px 17px;
}
</style>

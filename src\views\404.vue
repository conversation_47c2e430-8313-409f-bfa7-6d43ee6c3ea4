<template>
    <div class="not-found">
        <div class="not-found-content">
            <div class="not-found-icon">
                <el-icon size="120" color="#409EFF">
                    <Warning />
                </el-icon>
            </div>
            <h1 class="not-found-title">404</h1>
            <p class="not-found-message">抱歉，您访问的页面不存在</p>
            <div class="not-found-actions">
                <el-button type="primary" @click="goHome">
                    返回首页
                </el-button>
                <el-button @click="goBack">
                    返回上页
                </el-button>
            </div>
        </div>
    </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import { Warning } from '@element-plus/icons-vue'

const router = useRouter()

const goHome = () => {
    router.push('/introduce')
}

const goBack = () => {
    router.back()
}
</script>

<style scoped>
.not-found {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
    background-color: #f5f5f5;
}

.not-found-content {
    text-align: center;
    padding: 40px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.not-found-icon {
    margin-bottom: 20px;
}

.not-found-title {
    font-size: 72px;
    font-weight: bold;
    color: #409EFF;
    margin: 20px 0;
}

.not-found-message {
    font-size: 18px;
    color: #666;
    margin-bottom: 30px;
}

.not-found-actions {
    display: flex;
    justify-content: center;
    gap: 16px;
}
</style>

import axios from '@/utils/axios'

/**
 * 获取分类列表
 * @param {Object} params - 请求参数
 * @param {number} params.pageNumber - 页码
 * @param {number} params.pageSize - 每页数量
 * @param {number} [params.categoryLevel] - 分类级别
 * @param {number} [params.parentId] - 父级分类ID
 * @returns {Promise} - 返回Promise对象
 */
export function getCategoryList(params) {
  return axios.get('/categories', { params })
}

/**
 * 获取分类详情
 * @param {number} id - 分类ID
 * @returns {Promise} - 返回Promise对象
 */
export function getCategoryDetail(id) {
  return axios.get(`/categories/${id}`)
}

/**
 * 新增分类
 * @param {Object} data - 分类数据
 * @param {string} data.categoryName - 分类名称
 * @param {number} data.categoryRank - 分类排序
 * @param {string} data.categoryImg - 分类图片
 * @param {number} data.categoryType - 分类类型
 * @param {boolean} data.status - 状态
 * @param {number} data.categoryLevel - 分类等级
 * @param {number} [data.parentId] - 父级分类ID
 * @returns {Promise} - 返回Promise对象
 */
export function saveCategories(data) {
  return axios.post('/saveCategories', data)
}

/**
 * 更新分类
 * @param {Object} data - 分类数据
 * @param {number} data.categoryId - 分类ID
 * @param {string} data.categoryName - 分类名称
 * @param {number} data.categoryRank - 分类排序
 * @param {string} data.categoryImg - 分类图片
 * @param {number} data.categoryType - 分类类型
 * @param {boolean} data.status - 状态
 * @param {number} data.categoryLevel - 分类等级
 * @param {number} data.parentId - 父级分类ID
 * @returns {Promise} - 返回Promise对象
 */
export function updateCategories(data) {
  return axios.post('/updateCategories', data)
}

/**
 * 删除分类
 * @param {number} id - 分类ID
 * @returns {Promise} - 返回Promise对象
 */
export function deleteCategory(categoryId) {
  return axios.post('/deleteCategory', {
      categoryId
  }
  )
}

/**
 * 更新分类状态
 * @param {Object} data - 状态数据
 * @param {number} data.categoryId - 分类ID
 * @param {boolean} data.status - 状态
 * @returns {Promise} - 返回Promise对象
 */
export function updateCategoryStatus(params) {
  return axios.get('/updateStatus', { params })
}

import { createRouter, createWebHashHistory } from 'vue-router'
import { localGet } from '@/utils'
import { hasRoutePermission, clearUserPermissions } from '@/utils/permission'
import dynamicRouteManager from './dynamicRoutes'
import Merchant from '../views/Merchant.vue'

// 基础路由配置（不需要权限的路由） 不需要修改
const constantRoutes = [
    {
        path: '/',
        redirect: '/introduce'
    },
    {
        path: '/introduce',
        name: 'introduce',
        component: () => import(/* webpackChunkName: "introduce" */ '../views/Introduce.vue'),
        meta: { title: '系统介绍', requiresAuth: true }
    },
    {
        path: '/dashboard',
        name: 'dashboard',
        component: () => import(/* webpackChunkName: "dashboard" */ '../views/Index.vue'),
        meta: { title: '大盘数据', requiresAuth: true }
    },
    {
        path: '/login',
        name: 'login',
        component: () => import(/* webpackChunkName: "login" */ '../views/Login.vue'),
        meta: { title: '登录', requiresAuth: false }
    },
    // 404页面路由
    {
        path: '/:pathMatch(.*)*',
        name: 'NotFound',
        component: () => import(/* webpackChunkName: "404" */ '../views/404.vue'),
        meta: { title: '页面不存在', requiresAuth: false }
    }
]

// 需要权限控制的路由配置（将被动态添加）没有用到仅作为添加参考
export const asyncRoutes = [
    {
        path: '/add',
        name: 'add',
        component: () => import(/* webpackChunkName: "add" */ '../views/AddGoodSpec.vue'),
        meta: { title: '添加商品', requiresAuth: true, permission: 'goods:add' }
    },
    {
        path: '/swiper',
        name: 'swiper',
        component: () => import(/* webpackChunkName: "swiper" */ '../views/Swiper.vue'),
        meta: { title: '轮播图配置', requiresAuth: true, permission: 'config:swiper' }
    },
    {
        path: '/hot',
        name: 'hot',
        component: () => import(/* webpackChunkName: "hot" */ '../views/IndexConfig.vue'),
        meta: { title: '热销商品配置', requiresAuth: true, permission: 'config:hot' }
    },
    {
        path: '/new',
        name: 'new',
        component: () => import(/* webpackChunkName: "new" */ '../views/IndexConfig.vue'),
        meta: { title: '新品上线配置', requiresAuth: true, permission: 'config:new' }
    },
    {
        path: '/recommend',
        name: 'recommend',
        component: () => import(/* webpackChunkName: "recommend" */ '../views/IndexConfig.vue'),
        meta: { title: '为你推荐配置', requiresAuth: true, permission: 'config:recommend' }
    },
    {
        path: '/category',
        name: 'category',
        component: () => import(/* webpackChunkName: "category" */ '../views/Category.vue'),
        meta: { title: '分类管理', requiresAuth: true, permission: 'goods:category' },
        children: [
            {
                path: '/category/level2',
                name: 'level2',
                component: () => import(/* webpackChunkName: "level2" */ '../views/Category.vue'),
                meta: { title: '分类二级管理', requiresAuth: true, permission: 'goods:category' }
            },
            {
                path: '/category/level3',
                name: 'level3',
                component: () => import(/* webpackChunkName: "level3" */ '../views/Category.vue'),
                meta: { title: '分类三级管理', requiresAuth: true, permission: 'goods:category' }
            }
        ]
    },
    {
        path: '/good',
        name: 'good',
        component: () => import(/* webpackChunkName: "new" */ '../views/GoodNew.vue'),
        meta: { title: '商品管理', requiresAuth: true, permission: 'goods:manage' }
    },
    {
        path: '/guest',
        name: 'guest',
        component: () => import(/* webpackChunkName: "guest" */ '../views/Guest.vue'),
        meta: { title: '商户列表', requiresAuth: true, permission: 'merchant:list' }
    },
    {
        path: '/order',
        name: 'order',
        component: () => import(/* webpackChunkName: "order" */ '../views/Order.vue'),
        meta: { title: '订单列表', requiresAuth: true, permission: 'order:list' }
    },
    {
        path: '/order_detail',
        name: 'order_detail',
        component: () => import(/* webpackChunkName: "order_detail" */ '../views/OrderDetail.vue'),
        meta: { title: '订单详情', requiresAuth: true, permission: 'order:detail' }
    },
    {
        path: '/refundOrders',
        name: 'refundOrders',
        component: () => import(/* webpackChunkName: "refundOrders" */ '../views/refundOrders.vue'),
        meta: { title: '退款单', requiresAuth: true, permission: 'order:refund' }
    },
    {
        path: '/account',
        name: 'account',
        component: () => import(/* webpackChunkName: "account" */ '../views/Account.vue'),
        meta: { title: '修改账户', requiresAuth: true, permission: 'user:account' }
    },
    {
        path: '/specification',
        name: 'specification',
        component: () => import(/* webpackChunkName: "specification" */ '../views/Specification.vue'),
        meta: { title: '商品规格', requiresAuth: true, permission: 'goods:spec' }
    },
    {
        path: '/store',
        name: 'store',
        component: () => import(/* webpackChunkName: "account" */ '../views/Store.vue'),
        meta: { title: '店铺管理', requiresAuth: true, permission: 'merchant:manage' }
    },
    {
        path: '/merchants',
        name: 'merchants',
        component: () => import(/* webpackChunkName: "account" */ '../views/MerchantNew.vue'),
        meta: { title: '商户管理' }
    },
    {
        path: '/guests',
        name: 'guests',
        component: () => import(/* webpackChunkName: "account" */ '../views/Guest.vue'),
    },
    {
        path: '/supplier',
        name: 'supplier',
        component: () => import(/* webpackChunkName: "supplier" */ '../views/SupplierList.vue'),
        meta: { title: '供应商列表', requiresAuth: true, permission: 'supplier:list' }
    },
    {
        path: '/servicePackSetting',
        name: 'servicePackSetting',
        component: () => import(/* webpackChunkName: "account" */ '../views/servicePackSetting.vue'),
        meta: { title: '服务包配置', requiresAuth: true, permission: 'service:config' }
    },
    {
        path: '/fwbRefundOrders',
        name: 'fwbRefundOrders',
        component: () => import(/* webpackChunkName: "account" */ '../views/fwbRefundOrders.vue'),
        meta: { title: '服务包退款单', requiresAuth: true, permission: 'service:refund' }
    },
    {
        path: '/fwbOrder',
        name: 'fwbOrder',
        component: () => import(/* webpackChunkName: "account" */ '../views/fwbOrder.vue'),
        meta: { title: '服务包订单管理', requiresAuth: true, permission: 'service:order' }
    },
    {
        path: '/store',
        name: 'store',
        component: () => import(/* webpackChunkName: "account" */ '../views/Store.vue'),
        meta: { title: '门店管理', requiresAuth: true, permission: 'store:manage' }
    }
]


// 路由组件映射表 添加路径需要修改进去
//名称name=>组件路径
export const componentMap = {
    // 'Layout': () => import('@/components/DynamicMenu.vue'),
    'introduce': () => import('@/views/Introduce.vue'),
    'login': () => import('@/views/Login.vue'),
    'goods': () => import('@/views/GoodNew.vue'),
    'category': () => import('@/views/Category.vue'),
    'supplier': () => import('@/views/SupplierList.vue'),
    'specification': () => import('@/views/Specification.vue'),
    'add': () => import('@/views/AddGoodSpec.vue'),
    'orders': () => import('@/views/Order.vue'),
    'refundOrders': () => import('@/views/refundOrders.vue'),
    'store': () => import('@/views/Store.vue'),
    'merchants': () => import('@/views/MerchantNew.vue'),
    'guest': () => import('@/views/Guest.vue'),
    'pwdChange': () => import('@/views/Account.vue'),
    'servicePackSetting': () => import('@/views/servicePackSetting.vue'),
    // 'servicePackOrders': () => import('@/views/fwbOrders.vue'),
    // 'servicePackRefund': () => import('@/views/fwbRefundOrders.vue'),

    // 可以根据需要添加更多组件映射
}

//配置页面对应名称
export const pathMap = {
    login: '登录',
    introduce: '系统介绍',
    dashboard: '大盘数据',
    add: '添加商品',
    swiper: '轮播图配置',
    hot: '热销商品配置',
    new: '新品上线配置',
    recommend: '为你推荐配置',
    category: '分类管理',
    level2: '分类二级管理',
    level3: '分类三级管理',
    goods: '商品管理',
    store: '店铺管理',
    merchants: '商户管理',
    guest: '修改密码',
    supplier: '供应商列表',
    orders: '订单列表',
    order_detail: '订单详情',
    account: '修改账户',
    specification: '商品规格',
    refundOrders: "退款单",
    servicePackSetting: "服务包配置",
    servicePackRefund: "服务包退款单",
    servicePackOrders: "服务包订单管理",
}

const router = createRouter({
    history: createWebHashHistory(),
    routes: constantRoutes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
    const token = localGet('token')

    // 如果访问登录页面，直接放行
    if (to.path === '/login' || to.path === '/introduce') {

        next()
        return
    }
    // 如果已登录，检查是否已加载动态路由
    if (token && !dynamicRouteManager.hasRoutes()) {
        try {
            // 生成并添加动态路由
            if (await dynamicRouteManager.generateRoutes(router)) {
                // 重新导航到目标路由
                // console.log(to,'to')
                // console.log(to.path,'to.path')
                next({ path: to.path, replace: true })
                return
            } else {
                // 清除用户权限数据并重定向到登录页
                clearUserPermissions()
                next({ path: '/login' })
                return
            }
        } catch (error) {
            console.error('加载动态路由失败:', error)
            // 清除用户权限数据并重定向到登录页
            clearUserPermissions()
            next({ path: '/login' })
            return
        }
    }

    next()
})

// 路由后置守卫
router.afterEach((to) => {
    // 设置页面标题
    document.title = to.meta?.title || '健康商城管理系统'
})

export default router
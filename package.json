{"name": "admin00", "version": "0.0.0", "scripts": {"dev": "vite --mode development", "build": "vite build", "build:beta": "vite build --mode beta", "build:release": "vite build --mode release", "serve": "vite"}, "dependencies": {"@element-plus/icons-vue": "^2.0.9", "axios": "0.21.1", "element-plus": "^2.9.9", "js-md5": "^0.7.3", "unplugin-vue-components": "^0.22.7", "vue": "^3.2.39", "vue-router": "^4.1.5", "wangeditor": "^4.6.15"}, "devDependencies": {"@vitejs/plugin-vue": "2.3.3", "@vue/compiler-sfc": "3.0.5", "sass": "^1.54.9", "unplugin-auto-import": "^0.11.2", "unplugin-element-plus": "^0.4.1", "vite": "2.1.3", "vite-plugin-babel-import": "2.0.2"}}
<template>
  <el-dialog
    :title="type == 'add' ? '添加商户' : (type == 'edit' ? '修改商户' : '预览商户')"
    v-model="state.visible"
    width="600px"
  >
    <el-form :model="state.ruleForm" :rules="state.rules" ref="formRef" label-width="100px" class="good-form">
      
      <el-form-item required label="商户名称" prop="merchantName">
        <el-input type="text" placeholder="请输入商户名称" v-model="state.ruleForm.merchantName"></el-input>
      </el-form-item>
      <el-form-item required label="商户手机号" prop="merchantPhoneNo">
        <el-input type="text" placeholder="请输入手机号" clearable v-model="state.ruleForm.merchantPhoneNo"
        oninput="value=value.replace(/[^\d]/g,'')"
        ></el-input>
      </el-form-item>
      <el-text 
  :truncated="true" 
  :line-clamp="2" 
  type="primary" 
  size="medium"
  style="margin-left: 30px;"
>
商户手机号作为商户登录账号


</el-text>
      <!-- <el-form-item required label="商户logo" prop="merchantLogo">
        <el-upload
          class="avatar-uploader"
          :action="state.merchantLogo"
          accept="image/*"
          :headers="{
            token: state.token
          }"
          :auto-upload="false"
          :show-file-list="false"
          :before-upload="handleBeforeUpload"
          :on-change="handleUrlSuccess"
        >
          <img style="width: 200px; height: 100px; border: 1px solid #e9e9e9;" v-if="state.ruleForm.merchantLogo" :src="state.ruleForm.merchantLogo" class="avatar">
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload>
      </el-form-item> -->

      <el-form-item required label="状态" prop="status">
        <el-select 
          v-model="state.ruleForm.status"
          filterable 
          reserve-keyword
          clearable  
          placeholder="请选择状态" 
          style="width: 200px; margin-right: 10px">
          <el-option
            v-for="item in state.options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>

    </el-form>
    <template v-if="type == 'preview' ? false:  true" #footer>
      <span class="dialog-footer">
        <el-button @click="state.visible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, ref } from 'vue'
import axios from '@/utils/axios'
import { localGet, uploadImgServer } from '@/utils'
import { ElMessage } from 'element-plus'

const props = defineProps({
  type: String,
  reload: Function
})

const formRef = ref(null)
const state = reactive({
  uploadImgServer,
  token: localGet('token') || '',
  visible: false,
  ruleForm: {
    merchantName: '',
    merchantPhoneNo: '',
    merchantLogo: '',
    status: ''
  },
  rules: {
    merchantName: [
      { required: true, message: '商户名称不能为空', trigger: ['blur', 'change'] }
    ],
    merchantPhoneNo: [
      { required: true, message: '商户手机号不能为空', trigger: ['blur', 'change'] }
    ],
    status: [
      { required: true, message: '状态不能为空', trigger: ['change'] }
    ],
    merchantPhoneNo: [
    { 
      required: true, 
      message: '请输入手机号', 
      trigger: 'blur' 
    },
    { 
      type: 'number', 
      message: '手机号必须为数字', 
      trigger: 'blur',
      transform: (value) => Number(value) 
    },
    { 
      validator: (rule, value, callback) => {
        if (!/^1[3-9]\d{9}$/.test(value)) {
          callback(new Error('请输入正确的手机号'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  },
   // 订单状态筛选项默认值
   options: [
   {
    value: true,
    label: '开启'
  }, {
    value: false,
    label: '关闭'
  }
],
  id: '',
  logourl:''
})
// 获取详情
const getDetail = (id) => {
  axios.get(`/merchants/${id}`).then(res => {
    state.ruleForm = {
      merchantName: res.merchantName,
      merchantPhoneNo: res.merchantPhoneNo,
      merchantLogo: res.merchantLogo,
      status: res.status
    }
  })
}
const handleBeforeUpload = (file) => {
  const sufix = file.name.split('.')[1] || ''
  if (!['jpg', 'jpeg', 'png'].includes(sufix)) {
    ElMessage.error('请上传 jpg、jpeg、png 格式的图片')
    return false
  }
}
// 上传图片
const handleUrlSuccess = async (file) => {
  try {
    const previewUrl = await generatePreview(file.raw);
    state.ruleForm.merchantLogo = previewUrl || ''
    // console.log(state.ruleForm.merchantLogo,'previewUrl')
    // previewList.value.push(previewUrl);
    // form.images.push(file.raw);
    const formData = new FormData();
    formData.append('file', file.raw); // 文件字段
  // form.images.forEach((file, index) => {
  //   formData.append(`images[${index}]`, file);
  // });
  

  axios.post('/upload/file', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }).then(res => {
    // 处理成功逻辑
    // console.log(res, 'res');
    // if(res.resultCode === 200){
      state.ruleForm.merchantLogo = res.url
    // }
  });
  } catch (error) {
    console.error('生成预览失败:', error);
    ElMessage.error('文件读取失败');
  }
}
// 生成Base64预览
const generatePreview = (file) => {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target.result);
    reader.readAsDataURL(file);
  });
};
// 开启弹窗
const open = (id) => {
  state.visible = true
  if (id) {
    state.id = id
    getDetail(id)
  } else {
    state.ruleForm = {
      merchantName: '',
      merchantPhoneNo: '',
      merchantLogo: '',
      status: ''
    }
  }
}
// 关闭弹窗
const close = () => {
  state.visible = false
}
const submitForm = () => {
  console.log(formRef.value.validate)
  formRef.value.validate((valid) => {
    if (valid) {
      if (props.type == 'add') {
        axios.post('/merchants', {
          merchantName: state.ruleForm.merchantName,
          merchantPhoneNo: state.ruleForm.merchantPhoneNo,
          merchantLogo: state.ruleForm.merchantLogo,
          status: state.ruleForm.status
        }).then(() => {
          ElMessage.success('添加成功')
          state.visible = false
          if (props.reload) props.reload()
        })
      } else {
        axios.put('/merchants', {
          merchantId: state.id,
          merchantName: state.ruleForm.merchantName,
          merchantPhoneNo: state.ruleForm.merchantPhoneNo,
          merchantLogo: state.ruleForm.merchantLogo,
          status: state.ruleForm.status
        }).then(() => {
          ElMessage.success('修改成功')
          state.visible = false
          if (props.reload) props.reload()
        })
      }
    }
  })
}
defineExpose({ open, close })
</script>

<style scoped>
  .avatar-uploader {
    width: 100px;
    height: 100px;
    color: #ddd;
    font-size: 30px;
  }
  .avatar-uploader >>> .el-upload {
    width: 100%;
    text-align: center;
  }
  .avatar-uploader-icon {
    display: block;
    width: 100%;
    height: 100%;
    border: 1px solid #e9e9e9;
    padding: 32px 17px;
  }
</style>
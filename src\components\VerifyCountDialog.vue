<template>
  <el-dialog
    v-model="dialogVisible"
    width="400px"
    :show-close="false"
    :show-header="false"
    center
    class="verify-count-dialog"
  >
    <div class="verify-content">
      <div class="verify-title">
        <template v-if="type === 'verify'">
          该订单剩余{{ remainingCount }}份，请选择<br>本次需要核销份数
        </template>
        <template v-else>
          该订单可撤销{{ remainingCount }}份，请选择<br>本次需要撤销份数
        </template>
      </div>
      <div class="verify-input">
        <el-button @click="decreaseCount" :disabled="verifyCount <= 1" class="count-btn">-</el-button>
        <el-input-number
          v-model="verifyCount"
          :min="1"
          :max="remainingCount"
          :controls="false"
          class="count-input"
        />
        <el-button @click="increaseCount" :disabled="verifyCount >= remainingCount" class="count-btn">+</el-button>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" class="cancel-btn">取消</el-button>
        <el-button type="primary" @click="handleConfirm" class="confirm-btn">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch } from 'vue';
import { ElMessage } from 'element-plus';

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  orderNo: {
    type: String,
    default: ''
  },
  remainingCount: {
    type: Number,
    default: 1
  },
  type: {
    type: String,
    default: 'verify', // 'verify' 或 'cancelVerify'
    validator: (value) => ['verify', 'cancelVerify'].includes(value)
  }
});

const emit = defineEmits(['update:visible', 'confirm']);

const dialogVisible = ref(false);
const verifyCount = ref(1);

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal;
  if (newVal) {
    // 重置为1
    verifyCount.value = 1;
  }
});

// 监听dialogVisible变化，同步更新父组件的visible属性
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal);
});

// 增加数量
const increaseCount = () => {
  if (verifyCount.value < props.remainingCount) {
    verifyCount.value++;
  }
};

// 减少数量
const decreaseCount = () => {
  if (verifyCount.value > 1) {
    verifyCount.value--;
  }
};

// 取消操作
const handleCancel = () => {
  dialogVisible.value = false;
};

// 确认操作
const handleConfirm = () => {
  if (verifyCount.value <= 0 || verifyCount.value > props.remainingCount) {
    const errorMsg = props.type === 'verify' ? '核销份数无效' : '撤销份数无效';
    ElMessage.error(errorMsg);
    return;
  }

  emit('confirm', {
    orderNo: props.orderNo,
    count: verifyCount.value,
    type: props.type
  });

  dialogVisible.value = false;
};
</script>

<style scoped>
.verify-count-dialog {
  display: flex;
  align-items: center;
  justify-content: center;
}

.verify-count-dialog :deep(.el-dialog) {
  margin: 0 auto !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.verify-count-dialog :deep(.el-dialog__body) {
  padding: 30px 20px 20px;
}

.verify-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.verify-title {
  text-align: center;
  font-size: 28px;
  font-weight: bold;
  margin-bottom: 30px;
  line-height: 1.4;
}

.verify-input {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.count-btn {
  width: 50px;
  height: 50px;
  font-size: 24px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.count-input {
  width: 120px;
  margin: 0 20px;
  text-align: center;
}

.count-input :deep(input) {
  text-align: center;
  font-size: 28px;
  height: 50px;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  width: 100%;
  border-top: 1px solid #eee;
  padding-top: 10px;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  height: 60px;
  font-size: 24px;
  border: none;
  border-radius: 0;
  transition: all 0.3s;
}

.cancel-btn {
  color: #999;
  background-color: transparent;
}

.confirm-btn {
  color: #409eff;
  background-color: transparent;
}
</style>

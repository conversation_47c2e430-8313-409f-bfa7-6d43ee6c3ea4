<template>
  <el-card class="supplier-container">
    <template #header>
      <div class="header">
        <div class="search-section">
          <el-input
            v-model="state.supplierName"
            placeholder="请输入供应商名称"
            style="width: 200px; margin-right: 10px;"
            clearable
          />
          <el-select
            v-model="state.status"
            placeholder="请选择状态"
            style="width: 150px; margin-right: 10px;"
            clearable
          >
            <el-option label="开启" :value="1" />
            <el-option label="关闭" :value="0" />
          </el-select>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
        </div>
        <el-button type="primary" @click="handleAdd">添加供应商</el-button>
      </div>
    </template>
    
    <el-table
      :data="state.tableData"
      style="width: 100%"
      v-loading="state.loading"
    >
      <el-table-column label="序号" width="80" align="center">
        <template #default="{ $index }">
          {{ getContinuousIndex($index) }}
        </template>
      </el-table-column>
      
      <el-table-column prop="supplierName" label="供应商名称" align="center" />
      
      <el-table-column prop="supplierPhoneNo" label="供应商手机号" align="center" />
      
      <el-table-column prop="merchantNames" label="供应商户" align="center">
        <template #default="{ row }">
          <div v-if="row.merchantNames && row.merchantNames.length > 0" class="merchant-tags">
            <el-tag
              v-for="(merchant, index) in row.merchantNames"
              :key="index"
              style="margin: 2px; display: block;"
              size="small"
            >
              {{ merchant }}
            </el-tag>
          </div>
          <span v-else>-</span>
        </template>
      </el-table-column>
      
      <el-table-column prop="createTime" label="创建时间" align="center" width="180">
        <template #default="{ row }">
          {{ formatTime(row.createTime) }}
        </template>
      </el-table-column>
      
      <el-table-column label="状态" align="center" width="120">
        <template #default="{ row }">
          {{ row.status == 1 ? '开启' : '关闭'  }}
        </template>
      </el-table-column>
      
      <el-table-column label="操作" align="center" width="200">
        <template #default="{ row }">
          <el-button link type="primary" @click="handleEdit(row)">编辑</el-button>
          <el-button link type="primary" @click="handleResetPassword(row)">重置密码</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <el-pagination
      background
      layout="prev, pager, next, total"
      :total="state.total"
      :page-size="state.pageSize"
      :current-page="state.currentPage"
      @current-change="changePage"
      style="margin-top: 20px; text-align: right;"
    />
  </el-card>

  <!-- 添加供应商对话框 -->
  <DialogAddSupplier
    ref="addSupplierRef"
    type="add"
    :reload="getSupplierList"
  />

  <!-- 编辑供应商对话框 -->
  <DialogEditSupplier
    ref="editSupplierRef"
    type="edit"
    :reload="getSupplierList"
  />

  <!-- 重置密码确认对话框 -->
  <CustomConfirmDialog
    ref="confirmDialogRef"
    message="是否重置密码？"
    @confirm="confirmResetPassword"
    @cancel="cancelResetPassword"
  />
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import axios from '@/utils/axios'
import DialogAddSupplier from '@/components/DialogAddSupplier.vue'
import DialogEditSupplier from '@/components/DialogEditSupplier.vue'
import CustomConfirmDialog from '@/components/CustomConfirmDialog.vue'

const addSupplierRef = ref()
const editSupplierRef = ref()
const confirmDialogRef = ref()

const state = reactive({
  loading: false,
  tableData: [],
  total: 0,
  currentPage: 1,
  pageSize: 10,
  supplierName: '',
  status: '',
  currentResetSupplier: null
})

onMounted(() => {
  getSupplierList()
})

// 获取供应商列表
const getSupplierList = async () => {
  state.loading = true
  try {
    const params = {
      supplierName: state.supplierName || undefined,
      status: state.status !== '' ? state.status : undefined,
      pageNumber: state.currentPage,
      pageSize: state.pageSize
    }
    const res = await axios.get('/supplier/list', { params })
    console.log(res,'res.data')
    state.tableData = res.list || []
    state.total = res.totalCount || 0
  } catch (error) {
    console.error('获取供应商列表失败:', error)
    ElMessage.error('获取供应商列表失败')
  } finally {
    state.loading = false
  }
}

// 重置密码
const confirmResetPassword = async () => {
  if (state.currentResetSupplier) {
    try {
      await axios.post('/resetPassword', {
        supplierId: state.currentResetSupplier.supplierId
      })
      ElMessage.success('密码重置成功')
      state.currentResetSupplier = null
    } catch (error) {
      console.error('重置密码失败:', error)
      ElMessage.error('重置密码失败')
    }
  }
}

// 搜索
const handleSearch = () => {
  state.currentPage = 1
  getSupplierList()
}

// 重置搜索
const handleReset = () => {
  state.supplierName = ''
  state.status = ''
  state.currentPage = 1
  getSupplierList()
}

// 添加供应商
const handleAdd = () => {
  addSupplierRef.value.open()
}

// 编辑供应商
const handleEdit = (row) => {
  editSupplierRef.value.open(row)
}

// 状态变更
const handleStatusChange = (row, val) => {
  // 模拟API调用
  setTimeout(() => {
    ElMessage.success('状态更新成功')
    // 更新本地数据
    const item = state.tableData.find(item => item.supplierId === row.supplierId)
    if (item) {
      item.status = val
    }
  }, 300)
}

// 重置密码
const handleResetPassword = (row) => {
  state.currentResetSupplier = row
  confirmDialogRef.value.open()
}

// 确认重置密码
// const confirmResetPassword = () => {
//   if (state.currentResetSupplier) {
//     // 模拟API调用
//     setTimeout(() => {
//       ElMessage.success('已重置初始密码：123456')
//       state.currentResetSupplier = null
//     }, 300)
//   }
// }

// 取消重置密码
const cancelResetPassword = () => {
  state.currentResetSupplier = null
}

// 翻页
const changePage = (val) => {
  state.currentPage = val
  getSupplierList()
}

// 获取连续序号
const getContinuousIndex = (index) => {
  return (state.currentPage - 1) * state.pageSize + index + 1
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return '-'
  const date = new Date(timestamp)
  return date.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  }).replace(/\//g, '-')
}
</script>

<style scoped>
.supplier-container {
  margin: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-section {
  display: flex;
  align-items: center;
}
</style>

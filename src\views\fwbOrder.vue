<template>
  <el-card class="order-container">
    <template #header>
      <div >
        <el-input
          style="width: 200px; margin-right: 10px"
          placeholder="请输入用户手机号"
          v-model="state.phoneNo"
          @change="handleOption"
          clearable
        />
        <el-input
          style="width: 200px; margin-right: 10px"
          placeholder="请输入订单编号"
          v-model="state.orderNo"
          @change="handleOption"
          clearable
        />

        <el-select
          @change="handleOption"
          v-model="state.merchantId"
          placeholder="请选择商户"
          style="width: 200px; margin-right: 10px"
          clearable
        >
          <el-option
            v-for="item in state.merchantList"
            :key="item.merchantId"
            :label="item.merchantName"
            :value="item.merchantId"
          />
        </el-select>
        <el-config-provider :locale="state.locale">
          <el-date-picker
            @change="handleOption"
            v-model="state.dateRange"
            type="daterange"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            style="width: 260px; margin-right: 10px"
            clearable
          />
        </el-config-provider>
        <el-button type="primary" @click="handleOption()" style="margin-right: 10px">搜索</el-button>
        <el-button type="warning" @click="clearSearch()">重置</el-button>
      </div>
    </template>
    <div class="tab-container">
      <el-tabs v-model="state.activeTab" @tab-click="handleTabClick">
        <el-tab-pane :label="`全部${state.statusCounts['全部']}`" name="all"></el-tab-pane>
        <el-tab-pane :label="`待支付${state.statusCounts['待支付']}`" name="0"></el-tab-pane>
        <el-tab-pane :label="`待收货/使用${state.statusCounts['待收货/使用']}`" name="1"></el-tab-pane>
        <el-tab-pane :label="`已完成${state.statusCounts['已完成']}`" name="2"></el-tab-pane>
        <el-tab-pane :label="`已退款${state.statusCounts['已退款']}`" name="3"></el-tab-pane>
        <el-tab-pane :label="`已取消${state.statusCounts['已取消']}`" name="4"></el-tab-pane>
      </el-tabs>
    </div>
    <el-table
      :loading="state.loading"
      :data="state.tableData"
      tooltip-effect="dark"
      style="width: 100%">
      <el-table-column
        prop="orderNo"
        label="订单编号"
      >
      </el-table-column>
      <el-table-column
        prop="merchantName"
        label="商户名称"
      >
      </el-table-column>
      <el-table-column
        prop="phoneNo"
        label="用户手机号"
      >
      </el-table-column>
      <el-table-column
        prop="totalPrice"
        label="应付金额"
      >
      </el-table-column>
      <el-table-column
        prop="totalPrice"
        label="实付金额"
      >
      </el-table-column>
      <el-table-column
        prop="payType"
        label="支付方式"
      >
        <template #default='scope'>
          <span v-if="scope.row.payType == 1">支付宝支付</span>
          <span v-else-if="scope.row.payType == 2">微信支付</span>
          <span v-else-if="scope.row.payType == 0">无</span>
          <span v-else>未知</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="orderStatus"
        label="订单状态"
      >
        <template #default="scope">
          <span>{{ $filters.orderMap(scope.row.orderStatus) }}</span>
        </template>
      </el-table-column>
      <el-table-column
        label="创建时间"
      >
        <template #default="scope">
          {{ formatTimestamp(scope.row.createTime) }}
        </template>
      </el-table-column>
      <el-table-column
        label="操作"
        width="260"
      >
        <template #default="scope">
          <!-- 所有状态都显示详情按钮 -->
          <a class="operation-link" @click="showOrderDetail(scope.row.orderId)">详情</a>

          <!-- 待使用状态(1,2,3,6)显示核销按钮 -->
          <a
            v-if="[1, 2, 3, 6].includes(scope.row.orderStatus)"
            class="operation-link"
            @click="showVerifyDialog(scope.row.orderNo, 'verify')"
          >
            核销
          </a>

          <!-- 待使用状态(1,2,3,6)显示退款按钮 -->
          <a
            v-if="[1, 2, 3, 6].includes(scope.row.orderStatus)"
            class="operation-link"
            @click="showRefundDialog(scope.row.orderId)"
          >
            退款
          </a>

          <!-- 已完成状态且是核销成功(5)显示撤销核销按钮，为灰色但可点击 -->
          <a
            v-if="[1, 2, 3,4,5 ,6,-1,-2,-3].includes(scope.row.orderStatus)"
            class="operation-link disabled-link"
            @click="showVerifyDialog(scope.row.orderNo, 'cancelVerify')"
          >
            撤销核销
          </a>
        </template>
      </el-table-column>
    </el-table>
    <div class="pagination-container">
      <span class="total-count">共{{ Math.ceil(state.total / state.pageSize) }}页</span>
      <el-pagination
        background
        layout="prev, pager, next"
        :total="state.total"
        :page-size="state.pageSize"
        :current-page="state.currentPage"
        @current-change="changePage"
      />
    </div>
  </el-card>

  <!-- 订单详情弹窗 -->
  <OrderDetailDialog
    v-model:visible="detailDialogVisible"
    :order-id="currentOrderId"
  />

  <!-- 退款弹窗 -->
  <RefundDialog
    v-model:visible="refundDialogVisible"
    :order-id="refundOrderId"
    @refund-success="handleRefundSuccess"
  />

  <!-- 核销/撤销核销确认弹窗 -->
  <CustomConfirmDialog
    ref="confirmDialogRef"
    :message="verifyType === 'verify' ? '是否核销该订单?' : '是否撤销核销?'"
    confirm-text="是"
    cancel-text="否"
    @confirm="confirmVerify"
  />

  <!-- 核销/撤销核销份数选择弹窗 -->
  <VerifyCountDialog
    v-model:visible="verifyCountDialogVisible"
    :order-no="verifyOrderNo"
    :remaining-count="orderRemainingCount"
    :type="verifyDialogType"
    @confirm="handleVerifyCountConfirm"
  />
</template>

<script setup>
import { onMounted, reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import zhCn from "element-plus/es/locale/lang/zh-cn"
import axios from '@/utils/axios'
import OrderDetailDialog from '@/components/OrderDetailDialog.vue'
import RefundDialog from '@/components/RefundDialog.vue'
import CustomConfirmDialog from '@/components/CustomConfirmDialog.vue'
import VerifyCountDialog from '@/components/VerifyCountDialog.vue'

// 订单详情弹窗相关状态
const detailDialogVisible = ref(false)
const currentOrderId = ref('')

// 退款弹窗相关状态
const refundDialogVisible = ref(false)
const refundOrderId = ref('')

// 核销/撤销核销弹窗相关状态
const confirmDialogRef = ref(null)
const verifyOrderNo = ref('') // 存储订单号
const verifyType = ref('verify') // 'verify' 或 'cancelVerify'

// 核销/撤销核销份数弹窗相关状态
const verifyCountDialogVisible = ref(false)
const orderRemainingCount = ref(1) // 订单剩余可核销/撤销份数
const verifyDialogType = ref('verify') // 'verify' 或 'cancelVerify'

const state = reactive({
  loading: false,
  locale: zhCn,
  tableData: [], // 数据列表
  total: 0, // 总条数
  currentPage: 1, // 当前页
  pageSize: 10, // 分页大小
  orderNo: '', // 订单编号
  phoneNo: '', // 用户手机号
  dateRange: [], // 日期范围 [开始日期, 结束日期]
  merchantId: '', // 商户ID
  activeTab: 'all', // 当前选中的标签页
  status: '', // 订单状态查询参数
  merchantList: [], // 商户列表

  // 订单状态统计
  statusCounts: {
    '全部': 0,
    '待支付': 0,
    '待收货/使用': 0,
    '已完成': 0,
    '已退款': 0,
    '已取消': 0
  }
})

// 初始化获取订单列表和商户列表
onMounted(() => {
  getOrderList()
  getMerchantList()
  getOrderStatistics()
})

// 获取商户列表方法
const getMerchantList = () => {
  axios.get('/merchants', {
    params: {
      pageNumber: 1,
      pageSize: 999,
      status: 1 // 只获取状态为启用的商户
    }
  }).then(res => {
    state.merchantList = res.list || []
  })
}

// 获取订单列表方法
const getOrderList = () => {
  state.loading = true

  // 构建查询参数
  const params = {
    pageNumber: state.currentPage,
    pageSize: state.pageSize,
    orderNo: state.orderNo,
    phoneNo: state.phoneNo,
    startTime: state.dateRange && state.dateRange.length > 0 ? state.dateRange[0] : '',
    endTime: state.dateRange && state.dateRange.length > 1 ? state.dateRange[1] : '',
    merchantId: state.merchantId
  }

  // 添加订单状态查询参数（如果有）
  if (state.status) {
    params.status = state.status
  }

  axios.get('/orders', { params }).then(res => {
    state.tableData = res.list || []
    state.total = res.totalCount || 0
    state.currentPage = res.currPage || 1

    // 更新订单状态统计数据（实际项目中应该从后端获取）
    // 这里只是模拟数据
    // state.statusCounts = {
    //   all: res.totalCount || 0,
    //   '0': 0, // 待支付
    //   '1': 0, // 待确认
    //   '2': 0, // 待发货
    //   '3': 0, // 已发货
    //   '4': 0, // 交易成功
    // }

    state.loading = false
  }).catch(error => {
    console.error('获取订单列表失败:', error)
    state.loading = false
    ElMessage.error('获取订单列表失败')
  })
}

// 获取订单统计数据
const getOrderStatistics = () => {
  axios.get('/statistics').then(res => {
    if (res) {
      // 直接使用后端返回的数据格式
      state.statusCounts = res || {
        '全部': 0,
        '待支付': 0,
        '待收货/使用': 0,
        '已完成': 0,
        '已退款': 0,
        '已取消': 0
      }
    }
  }).catch(error => {
    console.error('获取订单统计数据失败:', error)
  })
}
// 触发过滤项方法
const handleOption = () => {
  state.currentPage = 1
  getOrderList()
}

// 清空搜索条件
const clearSearch = () => {
  state.orderNo = ''
  state.phoneNo = ''
  state.dateRange = []
  state.merchantId = ''
  state.status = ''
  state.activeTab = 'all'
  state.currentPage = 1
  getOrderList()
  getOrderStatistics()
}

// 处理标签页点击
const handleTabClick = (tab) => {
  // tab.paneName 包含被点击的标签页的 name 值
  console.log('切换到标签页:', tab.paneName)

  // 根据选中的标签页设置订单状态查询参数
  if (tab.paneName === 'all') {
    state.status = '' // 全部订单不需要状态过滤
  } else if (tab.paneName === '0') {
    state.status = '0' // 待支付
  } else if (tab.paneName === '1') {
    state.status = '1, 2, 3, 6' // 待收货/使用
  } else if (tab.paneName === '2') {
    state.status = '4, 5, -1, -2, -3' // 已完成
  } else if (tab.paneName === '3') {
    state.status = '-1, -3' // 已退款
  } else if (tab.paneName === '4') {
    state.status = '-1' // 已取消
  }

  state.currentPage = 1
  getOrderList()
  getOrderStatistics()
}

// 翻页方法
const changePage = (val) => {
  state.currentPage = val
  getOrderList()
}



// 显示核销对话框
const showVerifyDialog = (orderNo, type) => {
  if (!orderNo) {
    ElMessage.error('订单号不能为空')
    return
  }

  verifyOrderNo.value = orderNo
  verifyType.value = type
  verifyDialogType.value = type

  if (type === 'verify') {
    // 获取订单详情，查询剩余可核销份数
    getOrderRemainingCount(orderNo)
  } else {
    // 撤销核销 - 获取订单详情，查询可撤销核销份数
    getOrderCancelableCount(orderNo)
  }
}

// 获取订单剩余可核销份数
const getOrderRemainingCount = (orderNo) => {
  // 通过订单号获取订单ID
  const orderItem = state.tableData.find(item => item.orderNo === orderNo)
  if (!orderItem || !orderItem.orderId) {
    ElMessage.error('获取订单信息失败')
    return
  }

  // 获取订单详情
  axios.get(`/orders/${orderItem.orderId}`).then(res => {
    if (res && res.orderItemVOs && res.orderItemVOs.length > 0) {
      const orderItem = res.orderItemVOs[0]
      const totalCount = orderItem.goodsCount || 0
      const usedCount = orderItem.writeOffCount || 0
      const refundCount = orderItem.refundCount || 0

      // 计算剩余可核销份数 = 总数 - 已核销数 - 已退款数
      const remaining = totalCount - usedCount - refundCount

      if (remaining <= 0) {
        ElMessage.warning('该订单已无可核销份数')
        return
      }

      // 设置剩余份数并显示核销份数选择对话框
      orderRemainingCount.value = remaining
      verifyDialogType.value = 'verify'
      verifyCountDialogVisible.value = true
    } else {
      ElMessage.error('获取订单详情失败')
    }
  }).catch(error => {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败')
  })
}

// 处理核销/撤销核销份数确认
const handleVerifyCountConfirm = (data) => {
  if (!data.orderNo || !data.count || data.count <= 0) {
    ElMessage.error(data.type === 'verify' ? '核销参数无效' : '撤销核销参数无效')
    return
  }

  if (data.type === 'verify') {
    // 调用核销API，传入订单号和核销份数
    axios.get('/orderWriteOff', {
      params: {
        orderNo: data.orderNo,
        ct: data.count
      }
    }).then(() => {
      ElMessage.success('核销成功')
      getOrderList()
      getOrderStatistics() // 刷新订单统计数据
    }).catch(error => {
      console.error('核销失败:', error)
      ElMessage.error('核销失败')
    })
  } else {
    // 调用撤销核销API，传入订单号和撤销份数
    axios.get('orderWriteOffCancel', {
      params: {
        orderNo: data.orderNo,
        ct: data.count
      }
    }).then(() => {
      ElMessage.success('撤销核销成功')
      getOrderList()
      getOrderStatistics() // 刷新订单统计数据
    }).catch(error => {
      console.error('撤销核销失败:', error)
      ElMessage.error('撤销核销失败')
    })
  }
}

// 获取订单可撤销核销份数
const getOrderCancelableCount = (orderNo) => {
  // 通过订单号获取订单ID
  const orderItem = state.tableData.find(item => item.orderNo === orderNo)
  if (!orderItem || !orderItem.orderId) {
    ElMessage.error('获取订单信息失败')
    return
  }

  // 获取订单详情
  axios.get(`/orders/${orderItem.orderId}`).then(res => {
    if (res && res.orderItemVOs && res.orderItemVOs.length > 0) {
      const orderItem = res.orderItemVOs[0]
      const usedCount = orderItem.writeOffCount || 0
      const refundCount = orderItem.refundCount || 0

      // 计算可撤销核销份数 = 已核销数 - 已退款数
      const cancelable = usedCount - refundCount

      if (cancelable <= 0) {
        ElMessage.warning('该订单已无可撤销核销份数')
        return
      }

      // 设置可撤销份数并显示撤销核销份数选择对话框
      orderRemainingCount.value = cancelable
      verifyDialogType.value = 'cancelVerify'
      verifyCountDialogVisible.value = true
    } else {
      ElMessage.error('获取订单详情失败')
    }
  }).catch(error => {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败')
  })
}

// 处理核销确认 - 已不再使用，由handleVerifyCountConfirm和handleCancelVerifyCountConfirm替代
const confirmVerify = () => {
  // 此方法保留但不再使用，为了兼容性
}

// 显示订单详情弹窗
const showOrderDetail = (id) => {
  if (!id) {
    ElMessage.error('订单ID不能为空')
    return
  }

  currentOrderId.value = id
  detailDialogVisible.value = true
}

// 显示退款弹窗
const showRefundDialog = (id) => {
  if (!id) {
    ElMessage.error('订单ID不能为空')
    return
  }

  refundOrderId.value = id
  refundDialogVisible.value = true
}

// 处理退款成功
const handleRefundSuccess = () => {
  // 刷新订单列表
  getOrderList()
  getOrderStatistics()
}

// 格式化时间戳为日期时间字符串
const formatTimestamp = (timestamp) => {
  if (!timestamp) return '暂无数据'

  // 判断是否为数字字符串，如果是则转换为数字
  if (typeof timestamp === 'string' && !isNaN(timestamp)) {
    timestamp = parseInt(timestamp)
  }

  // 创建日期对象
  const date = new Date(timestamp)

  // 检查日期是否有效
  if (isNaN(date.getTime())) return '无效日期'

  // 格式化日期
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}
</script>

<style scoped>
.order-container {
  min-height: 100%;
}

.header {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 20px;
}

.tab-container {
  margin-bottom: 20px;
}

.operation-link {
  margin-right: 15px;
  color: #409EFF;
  cursor: pointer;
}

.disabled-link {
  color: #C0C4CC;
  cursor: pointer;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 20px;
}

.total-count {
  margin-right: 15px;
  color: #606266;
}

/* 响应式布局调整 */
@media screen and (max-width: 1200px) {
  .header {
    flex-direction: column;
    align-items: flex-start;
  }

  .header > * {
    margin-bottom: 10px;
  }
}
</style>
<template>
  <el-card class="order-container">
    <template #header>
      <div class="header">
        <el-input
        v-if="false"
          style="width: 200px; margin-right: 10px"
          placeholder="  请输入店铺名称"
          v-model="state.orderNo"
          filterable 
          reserve-keyword
          clearable
        />
        <el-select  filterable 
          reserve-keyword
          clearable  placeholder="请选择状态" v-model="selectedstateId" style="width: 200px; margin-right: 10px">
          <el-option
            v-for="item in state.options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <!-- <el-button type="primary" size="small" icon="el-icon-edit">修改订单</el-button> -->
        <el-button type="primary" :icon="HomeFilled" @click="handleOption">搜索</el-button>
        <el-button type="primary" :icon="HomeFilled" @click="handleOption('1')">重置</el-button>
        <el-button v-if="false" type="danger" :icon="Delete" @click="handleAdd">添加店铺</el-button>
      </div>
    </template>
    <el-table
      :load="state.loading"
      :data="state.tableData"
      tooltip-effect="dark"
      style="width: 100%"
      @selection-change="handleSelectionChange">
      <!-- <el-table-column
        type="selection"
        width="55">
      </el-table-column> -->
      <el-table-column label="序号" align="center" width="80">
      <template #default="{ $index }">
        {{ getContinuousIndex($index) }}
      </template>
    </el-table-column>
    <el-table-column v-if="false" label="店铺logo" align="center" width="180">
  <template #default="scope">
    <el-image
      :src="$filters.prefix(scope.row.merchantLogo)"
      :preview-src-list="[scope.row.merchantLogo]"
      style="width: 80px; height: 80px"
      fit="cover"
    />
  </template>
</el-table-column>

      <el-table-column
        prop="merchantName"
        label="服务包名称"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="merchantPhoneNo"
        label="服务包金额"
        align="center"
      >
      </el-table-column>
      <el-table-column
      v-if="false"
        prop="merchantBelong"
        label="所属商户"
      >
      </el-table-column>
      <!-- <el-table-column
        prop="orderStatus"
        label="创建类型"
      >
        <template #default="scope">
          <span>{{ $filters.orderMap(scope.row.orderStatus) }}</span>
        </template>
      </el-table-column> -->
      <el-table-column
        prop="createType"
        label="创建类型"
        v-if="false"
        align="center"
      >
        <template #default='scope'>
          <span v-if="scope.row.createType == 1">微信支付</span>
          <span v-else-if="scope.row.createType == 2">支付宝支付</span>
          <span v-else>未知</span>
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        v-if="false"
        align="center"
      >
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="120">
  <template #default="scope">
    <span>{{ scope.row.status ? '开启' : '关闭' }}</span>
  </template>
</el-table-column>
      <el-table-column
        label="操作" align="center"
        
      >
        <template #default="scope">
        
          <!-- <el-popconfirm
            v-if="scope.row.orderStatus == 2"
            title="确定出库吗？"
            @confirm="handleSend(scope.row.orderId)"
            confirm-button-text="确定"
            cancel-button-text="取消"
          >
            <template #reference>
              <a style="cursor: pointer; margin-right: 10px">预览</a>
            </template>
          </el-popconfirm> -->
          <el-text 
         
         :truncated="true" 
         :line-clamp="2" 
         type="primary" 
         size="medium"
         style="cursor: pointer; "
         @click="handlePreview(scope.row.merchantId)" 
       >
       详情
       
       
       </el-text>
          <el-text 
         v-if="false"
  :truncated="true" 
  :line-clamp="2" 
  type="primary" 
  size="medium"
  style="margin-left: 30px;cursor: pointer; "
  @click="handleEdit(scope.row.merchantId,scope.row.status)" 
>
编辑


</el-text>
          <!-- <el-popconfirm
            v-if="!(scope.row.orderStatus == 4 || scope.row.orderStatus < 0)"
            title="确定关闭订单吗？"
            @confirm="handleClose(scope.row.orderId)"
            confirm-button-text="确定"
            cancel-button-text="取消"
          >
            <template #reference>
              <a style="cursor: pointer; margin-right: 10px">编辑</a>
            </template>
          </el-popconfirm> -->
          <el-text 
         v-if="false"
         :truncated="true" 
         :line-clamp="2" 
         type="primary" 
         size="medium"
         style="margin-left: 30px;cursor: pointer; "
         @click="confirmReset(scope.row.merchantId)" 
       >
       重置密码
       
       
       </el-text>
          <!-- <router-link :to="{ path: '/order_detail', query: { id: scope.row.orderId }}"> 重置密码</router-link> -->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
       v-model:current-page="currentPage"
      layout="prev, pager, next"
      :total="state.total"
      :page-size="state.pageSize"
      :current-page="state.currentPage"
      @current-change="changePage"
    />
  </el-card>
  <DialogAddSwiper ref='addSwiper' :reload="getOrderList" :type="state.type" />
  <el-dialog
    v-model="dialogVisible"
    title="是否重置密码？"
    width="420px"
    :show-close="false"
    custom-class="reset-password-dialog"
  >
    <!-- 弹窗底部操作区 -->
    <template #footer>
      <div class="dialog-footer">
        <el-button 
          class="full-width-btn" 
          @click="dialogVisible = false"
        >否</el-button>
        <el-button 
          type="primary" 
          class="full-width-btn"
          @click="confirmResetYes"
        >是</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { onMounted, reactive,ref, toRefs } from 'vue'
import { ElMessage } from 'element-plus'
import { HomeFilled, Delete } from '@element-plus/icons-vue'
import axios from '@/utils/axios'
import DialogAddSwiper from '@/components/DialogReviewdetails.vue'
const addSwiper = ref()
const state = reactive({
  loading: false,
  tableData: [], // 数据列表
  multipleSelection: [], // 选中项
  total: 0, // 总条数
  currentPage: 1, // 当前页
  pageSize: 10, // 分页大小
  orderNo: '', // 订单号
  orderStatus: '', // 订单状态
  type: 'add', // 操作类型,
  orderId: '',
  // 订单状态筛选项默认值
  options: [
   {
    value: 0,
    label: '审核中'
  }, {
    value: 1,
    label: '审核不通过'
  }, {
    value: 2,
    label: '审核通过'
  }
]
})
// 初始化获取订单列表
onMounted(() => {
  getOrderList()
})
const selectedstateId = ref([0]);
// 弹窗显示控制
const dialogVisible = ref(false)

// 确认重置密码
const confirmReset = (id) => {
  dialogVisible.value = true
  state.orderId = id


  // ElMessage.success('已重置初始密码 "123456"')
  // 此处可添加实际重置逻辑
  // axios.post('/reset-password', { newPassword: '123456' })
}

const handleStatus = (id, merchantName,merchantPhoneNo,merchantLogo,val) => {
  console.log(val,'status')
  const statustemp = ref(0); 
  if(val){
    statustemp.value = 1
  }else{
    statustemp.value = 0
  }
  console.log(statustemp.value,'statustemp')
  axios.put('/merchants', {
    merchantId: id,
    merchantName: merchantName,
      merchantPhoneNo: merchantPhoneNo,
      merchantLogo: merchantLogo,
    status: statustemp.value
  }).then(() => {
    ElMessage.success('修改成功')
    getOrderList()
  })
}

const confirmResetYes = () => {


  axios.put('/merchants/password/reset', {
          merchantId: state.orderId
         
        }).then(() => {
          ElMessage.success('已重置初始密码 "123456"')
          dialogVisible.value = false
          // if (props.reload) props.reload()
        })
  
  // 此处可添加实际重置逻辑
  // axios.post('/reset-password', { newPassword: '123456' })
}

// 添加轮播项
const handleAdd = () => {
  console.log('addSwiper', addSwiper.value)
  state.type = 'add'
  addSwiper.value.open()
}
// 修改轮播图
const handleEdit = (id,status) => {
  state.type = 'edit'
  addSwiper.value.open(id,status)
}
// 
const handlePreview = (id) => {
  state.type = 'preview'
  addSwiper.value.open(id)
}

const handleStockAlertChange = (index) => {
  ElMessage.info(`状态已更新: ${state.tableData[index].isDeleted ? '开启' : '关闭'}`);
};

const getContinuousIndex = (index) => {
  // console.log((state.currentPage - 1) ,'index')
  return (state.currentPage - 1) * state.pageSize + index + 1;
};

// 获取列表方法
const getOrderList = () => {
  state.loading = true

  console.log(selectedstateId ,'index')
  axios.get('/merchants', {
    params: {
      pageNumber: state.currentPage,
      pageSize: state.pageSize,
      merchantName: state.orderNo,
      status: selectedstateId.value=== undefined?'':selectedstateId.value.length?'':selectedstateId.value
    }
  }).then(res => {
    state.tableData = res.list
    state.total = res.totalCount
    state.currentPage = res.currPage
    state.loading = false
  })
}
// 触发过滤项方法
const handleOption = (index) => {
  // console.log(index,'index')
  if(index === '1'){
    state.orderNo = ''
    selectedstateId.value = ''
  }
  state.currentPage = 1
  getOrderList()
}
// checkbox 选择项
const handleSelectionChange = (val) => {
  state.multipleSelection = val
}
// 翻页方法
const changePage = (val) => {
  state.currentPage = val
  getOrderList()
}
// 配货方法
const handleConfig = (id) => {
  let params
  // 当个配置
  if (id) {
    params = [id]
  } else {
    if (!state.multipleSelection.length) {
      console.log('state.multipleSelection', state.multipleSelection.length)
      ElMessage.error('请选择项')
      return
    }
    // 多选配置
    params = state.multipleSelection.map(i => i.orderId)
  }
  axios.put('/orders/checkDone', {
    ids: params
  }).then(() => {
    ElMessage.success('配货成功')
    getOrderList()
  })
}
// 出库方法
const handleSend = (id) => {
  let params
  if (id) {
    params = [id]
  } else {
    if (!state.multipleSelection.length) {
      ElMessage.error('请选择项')
      return
    }
    params = state.multipleSelection.map(i => i.orderId)
  }
  axios.put('/orders/checkOut', {
    ids: params
  }).then(() => {
    ElMessage.success('出库成功')
    getOrderList()
  })
}
// 关闭订单方法
const handleClose = (id) => {
  let params
  if (id) {
    params = [id]
  } else {
    if (!state.multipleSelection.length) {
      ElMessage.error('请选择项')
      return
    }
    params = state.multipleSelection.map(i => i.orderId)
  }
  axios.put('/orders/close', {
    ids: params
  }).then(() => {
    ElMessage.success('关闭成功')
    getOrderList()
  })
}
</script>

<style lang="scss">
.reset-password-dialog {
  /* 标题样式 */
  .el-dialog__header {
    text-align: center;
    padding: 20px 20px 10px;
    
    .el-dialog__title {
      font-size: 18px;
      color: #333;
    }
  }

  /* 底部操作区 */
  .dialog-footer {
    display: flex;
    gap: 15px;
    padding: 0 20px 20px;

    .full-width-btn {
      flex: 1;
      height: 40px;
    }
  }

  /* 去除默认底部边距 */
  .el-dialog__body {
    padding: 0;
  }
}
</style>
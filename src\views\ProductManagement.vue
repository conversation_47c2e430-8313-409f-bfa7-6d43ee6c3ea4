<template>
  <el-card class="product-container">
    <template #header>
      <div class="header">
        <!-- 搜索和筛选区域 -->
        <el-input
          style="width: 200px; margin-right: 10px"
          placeholder="请输入商品名称"
          v-model="state.searchName"
          clearable
        />
        
        <el-select 
          filterable 
          reserve-keyword 
          clearable 
          placeholder="请选择商户" 
          v-model="selectedMerchantId" 
          style="width: 200px; margin-right: 10px"
        >
          <el-option
            v-for="item in merchantOptions"
            :key="item.merchantId"
            :label="item.merchantName"
            :value="item.merchantId"
          />
        </el-select>
        
        <el-select 
          filterable 
          reserve-keyword 
          clearable 
          placeholder="请选择供应商" 
          v-model="selectedSupplierId" 
          style="width: 200px; margin-right: 10px"
        >
          <el-option
            v-for="item in supplierOptions"
            :key="item.supplierId"
            :label="item.supplierName"
            :value="item.supplierId"
          />
        </el-select>
        
        <el-select 
          filterable 
          reserve-keyword 
          clearable 
          placeholder="请选择状态" 
          v-model="selectedStatusId" 
          style="width: 200px; margin-right: 10px"
        >
          <el-option
            v-for="item in statusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        
        <el-select 
          filterable 
          reserve-keyword 
          clearable 
          placeholder="请选择审核状态" 
          v-model="selectedAuditStatusId" 
          style="width: 200px; margin-right: 10px"
        >
          <el-option
            v-for="item in auditStatusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        
        <div style="margin-top: 10px;">
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button type="primary" @click="handleReset">重置</el-button>
          <el-button type="primary" :icon="Plus" @click="handleAddProduct">添加商品</el-button>
        </div>
      </div>
    </template>
    
    <!-- 商品列表表格 -->
    <el-table
      v-loading="state.loading"
      :data="state.tableData"
      tooltip-effect="dark"
      style="width: 100%"
    >
      <el-table-column type="selection" width="55" align="center" />
      
      <el-table-column
        align="center"
        prop="goodsId"
        label="ID"
      />
      
      <el-table-column
        align="center"
        label="商品图片"
        width="120"
      >
        <template #default="scope">
          <img 
            style="width: 80px; height: 80px;" 
            :key="scope.row.goodsId" 
            :src="$filters.prefix(scope.row.goodsCoverImg)" 
            alt="商品主图"
          >
        </template>
      </el-table-column>
      
      <el-table-column
        align="center"
        prop="goodsName"
        label="商品名称"
      />
      
      <el-table-column
        align="center"
        prop="merchantName"
        label="商户名称"
      />
      
      <el-table-column
        align="center"
        prop="supplierName"
        label="供应商名称"
      />
      
      <el-table-column
        align="center"
        prop="goodsSellingPrice"
        label="商品售价"
      />
      
      <el-table-column
        align="center"
        prop="goodsSellCount"
        label="销量"
      />
      
      <el-table-column
        align="center"
        prop="goodsStockNum"
        label="库存"
      />
      
      <el-table-column 
        label="状态" 
        align="center" 
        width="120"
      >
        <template #default="{ row }">
          <span v-if="row.goodsSellStatus === 1">上架</span>
          <span v-else-if="row.goodsSellStatus === 0">下架</span>
        </template>
      </el-table-column>
      
      <el-table-column 
        label="审核状态" 
        align="center" 
        width="120"
      >
        <template #default="{ row }">
          <span v-if="row.auditStatus === 0">待审核</span>
          <span v-else-if="row.auditStatus === 1">审核通过</span>
          <span v-else-if="row.auditStatus === 2">审核不通过</span>
        </template>
      </el-table-column>
      
      <el-table-column
        align="center"
        label="操作"
        width="200"
      >
        <template #default="scope">
          <!-- 审核中状态 -->
          <template v-if="scope.row.auditStatus === 0">
            <a style="cursor: pointer; margin-right: 10px" @click="handleAudit(scope.row)">审核</a>
          </template>
          
          <!-- 上架状态 -->
          <template v-if="scope.row.goodsSellStatus === 1 && scope.row.auditStatus === 1">
            <a style="cursor: pointer; margin-right: 10px" @click="handleEdit(scope.row.goodsId, 'preview')">预览</a>
            <a style="cursor: pointer; margin-right: 10px" @click="handleEdit(scope.row.goodsId, 'edit')">编辑</a>
            <a style="cursor: pointer; margin-right: 10px" @click="handleTakeDown(scope.row)">下架</a>
          </template>
          
          <!-- 下架状态 -->
          <template v-if="scope.row.goodsSellStatus === 0">
            <a style="cursor: pointer; margin-right: 10px" @click="handleEdit(scope.row.goodsId, 'preview')">预览</a>
            <a style="cursor: pointer; margin-right: 10px" @click="handleEdit(scope.row.goodsId, 'edit')">编辑</a>
            <a style="cursor: pointer; margin-right: 10px" @click="handleSubmitAudit(scope.row)">提交审核</a>
            <a style="cursor: pointer; margin-right: 10px" @click="handleCopy(scope.row.goodsId)">复制</a>
            <a style="cursor: pointer; margin-right: 10px" @click="handleDelete(scope.row.goodsId)">删除</a>
          </template>
          
          <!-- 审核不通过状态 -->
          <template v-if="scope.row.auditStatus === 2">
            <a style="cursor: pointer; margin-right: 10px" @click="handleEdit(scope.row.goodsId, 'preview')">预览</a>
            <a style="cursor: pointer; margin-right: 10px" @click="handleEdit(scope.row.goodsId, 'edit')">编辑</a>
            <a style="cursor: pointer; margin-right: 10px" @click="handleSubmitAudit(scope.row)">提交审核</a>
            <a style="cursor: pointer; margin-right: 10px" @click="handleCopy(scope.row.goodsId)">复制</a>
            <a style="cursor: pointer; margin-right: 10px" @click="handleDelete(scope.row.goodsId)">删除</a>
          </template>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 分页器 -->
    <el-pagination
      background
      layout="prev, pager, next"
      :total="state.total"
      :page-size="state.pageSize"
      :current-page="state.currentPage"
      @current-change="changePage"
    />
  </el-card>
  
  <!-- 审核对话框 -->
  <DialogAudit ref="auditDialogRef" :reload="getProductList" />
  
  <!-- 确认对话框 -->
  <CustomConfirmDialog
    ref="confirmDialogRef"
    :message="confirmMessage"
    confirm-text="确认"
    cancel-text="取消"
    @confirm="confirmAction"
  />
</template>

<script setup>
import { onMounted, reactive, ref, getCurrentInstance } from 'vue'
import axios from '@/utils/axios'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import DialogAudit from '@/components/DialogAudit.vue'
import CustomConfirmDialog from '@/components/CustomConfirmDialog.vue'

const app = getCurrentInstance()
const { goTop } = app.appContext.config.globalProperties
const router = useRouter()

// 引用组件
const auditDialogRef = ref(null)
const confirmDialogRef = ref(null)

// 状态管理
const state = reactive({
  loading: false,
  tableData: [], // 数据列表
  total: 0, // 总条数
  currentPage: 1, // 当前页
  pageSize: 10, // 分页大小
  searchName: '', // 商品名称搜索
})

// 选择项
const selectedMerchantId = ref('')
const selectedSupplierId = ref('')
const selectedStatusId = ref('')
const selectedAuditStatusId = ref('')

// 确认对话框相关
const confirmMessage = ref('确认执行此操作?')
const confirmCallback = ref(null)
const confirmData = ref(null)

// 选项数据
const merchantOptions = ref([])
const supplierOptions = ref([])

// 状态选项
const statusOptions = [
  { value: 1, label: '上架' },
  { value: 0, label: '下架' }
]

// 审核状态选项
const auditStatusOptions = [
  { value: 0, label: '待审核' },
  { value: 1, label: '审核通过' },
  { value: 2, label: '审核不通过' }
]

// 初始化
onMounted(() => {
  getProductList()
  getMerchantList()
  getSupplierList()
})

// 获取商户列表
const getMerchantList = () => {
  axios.get('/merchants', {
    params: {
      pageNumber: 1,
      pageSize: 100,
      merchantName: '',
      status: ''
    }
  }).then(res => {
    merchantOptions.value = res.list || []
  })
}

// 获取供应商列表
const getSupplierList = () => {
  axios.get('/suppliers', {
    params: {
      pageNumber: 1,
      pageSize: 100
    }
  }).then(res => {
    supplierOptions.value = res.list || []
  }).catch(err => {
    console.error('获取供应商列表失败:', err)
    supplierOptions.value = [] // 如果API不存在，设置为空数组
  })
}

// 获取商品列表
const getProductList = () => {
  state.loading = true
  axios.get('/goods/list', {
    params: {
      pageNumber: state.currentPage,
      pageSize: state.pageSize,
      goodsName: state.searchName || undefined,
      merchantId: selectedMerchantId.value || undefined,
      supplierId: selectedSupplierId.value || undefined,
      goodsSellStatus: selectedStatusId.value !== '' ? selectedStatusId.value : undefined,
      auditStatus: selectedAuditStatusId.value !== '' ? selectedAuditStatusId.value : undefined
    }
  }).then(res => {
    state.tableData = res.list || []
    state.total = res.totalCount || 0
    state.currentPage = res.currPage || 1
    state.loading = false
    goTop && goTop()
  }).catch(err => {
    console.error('获取商品列表失败:', err)
    state.loading = false
    ElMessage.error('获取商品列表失败')
  })
}

// 搜索
const handleSearch = () => {
  state.currentPage = 1
  getProductList()
}

// 重置
const handleReset = () => {
  state.searchName = ''
  selectedMerchantId.value = ''
  selectedSupplierId.value = ''
  selectedStatusId.value = ''
  selectedAuditStatusId.value = ''
  state.currentPage = 1
  getProductList()
}

// 添加商品
const handleAddProduct = () => {
  router.push({ path: '/add' })
}

// 编辑商品
const handleEdit = (id, option) => {
  router.push({ path: '/add', query: { id, option } })
}

// 复制商品
const handleCopy = (id) => {
  router.push({ path: '/add', query: { id, option: 'copy' } })
}

// 审核商品
const handleAudit = (row) => {
  auditDialogRef.value.open(row)
}

// 提交审核
const handleSubmitAudit = (row) => {
  confirmMessage.value = '确认提交审核吗？'
  confirmCallback.value = submitAudit
  confirmData.value = row
  confirmDialogRef.value.open()
}

// 执行提交审核
const submitAudit = () => {
  if (!confirmData.value || !confirmData.value.goodsId) {
    ElMessage.error('商品ID不能为空')
    return
  }

  axios.post('/goods/submit-audit', {
    goodsId: confirmData.value.goodsId
  }).then(() => {
    ElMessage.success('提交审核成功')
    getProductList()
  }).catch(err => {
    console.error('提交审核失败:', err)
    ElMessage.error('提交审核失败')
  })
}

// 下架商品
const handleTakeDown = (row) => {
  confirmMessage.value = '确认下架该商品吗？下架后需要重新审核才能上架'
  confirmCallback.value = takeDownProduct
  confirmData.value = row
  confirmDialogRef.value.open()
}

// 执行下架
const takeDownProduct = () => {
  if (!confirmData.value || !confirmData.value.goodsId) {
    ElMessage.error('商品ID不能为空')
    return
  }

  axios.put(`/goods/status/0`, {
    ids: [confirmData.value.goodsId]
  }).then(() => {
    ElMessage.success('下架成功')
    getProductList()
  }).catch(err => {
    console.error('下架失败:', err)
    ElMessage.error('下架失败')
  })
}

// 删除商品
const handleDelete = (id) => {
  confirmMessage.value = '确认删除该商品吗？'
  confirmCallback.value = deleteProduct
  confirmData.value = { goodsId: id }
  confirmDialogRef.value.open()
}

// 执行删除
const deleteProduct = () => {
  if (!confirmData.value || !confirmData.value.goodsId) {
    ElMessage.error('商品ID不能为空')
    return
  }

  axios.delete(`/goods/${confirmData.value.goodsId}`).then(() => {
    ElMessage.success('删除成功')
    getProductList()
  }).catch(err => {
    console.error('删除失败:', err)
    ElMessage.error('删除失败')
  })
}

// 确认操作
const confirmAction = () => {
  if (confirmCallback.value) {
    confirmCallback.value()
  }
}

// 分页
const changePage = (val) => {
  state.currentPage = val
  getProductList()
}
</script >
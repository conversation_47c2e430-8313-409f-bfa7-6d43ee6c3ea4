<template>
  
  <div class="add">
    <el-tabs 
      v-model="activeName" 
      type="border-card" 
      class="fixed-tabs"
      @tab-click="handleTabClick"
    >
      <el-tab-pane label="商品信息" name="1" />
      <el-tab-pane label="商品设置" name="2" />
    </el-tabs>
    <el-card v-show="activeName=== '1'" class="add-container">
      <el-form :model="state.goodForm" :rules="state.rules" ref="goodRef" label-width="100px" class="goodForm">
        
        <el-form-item label="商品名称" prop="goodsName">
          <el-input clearable filterable 
          reserve-keyword  style="width: 300px" v-model="state.goodForm.goodsName" placeholder="请输入商品名称"></el-input>
        </el-form-item>
        <el-form-item label="商品副名称" prop="goodsSubName">
          <el-input clearable filterable 
          reserve-keyword  style="width: 300px" v-model="state.goodForm.goodsSubName" placeholder="请输入商品副名称"></el-input>
        </el-form-item>
        <el-form-item  label="商品分类" prop="categoryId">
          <el-cascader clearable filterable  
          reserve-keyword :disabled="option === 'edit'" ref="cascaderRef"  v-model="selectedValuecategory" :show-all-levels="false" :placeholder="state.defaultCate" style="width: 300px" :props="state.category" @change="handleChangeCate"></el-cascader>
        </el-form-item>
        <el-form-item v-if="false"  label="所属类型"  prop="selectedId">
          <el-select clearable filterable  
        reserve-keyword :disabled="option === 'edit'" placeholder="请选择商品归属"  v-model="selectedId" :options="options" style="width: 300px; margin-right: 10px">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>       
       </el-form-item>
        <el-form-item  label="所属商户" prop="goodsBelongMerchantId" >
          <el-select clearable filterable 
        reserve-keyword  placeholder="请选择商户" 
        v-model="selectedValuemerchants" style="width: 300px; margin-right: 10px"
        :disabled="isMerchantDisabled"
        >
          <el-option
            v-for="item in optionsmerchants"
            :key="item.merchantId"
            :label="item.merchantName"
            :value="item.merchantId"
          />
        </el-select>

        </el-form-item>
        <el-form-item  label="所属店铺" prop="goodsBelongShopId" >
          <el-select clearable filterable 
        reserve-keyword  placeholder="请选择店铺" 
        v-model="selectedValueStore" 
        style="width: 300px; margin-right: 10px">
          <el-option
            v-for="item in optionsstore"
            :key="item.shopId"
            :label="item.shopName"
            :value="item.shopId"
          />
        </el-select>

        </el-form-item>
        <el-form-item  label="所属供应商" prop="goodsSupplierId" >
          <el-select clearable filterable 
        reserve-keyword  placeholder="请选择供应商" 
        v-model="selectedValueSupply"
        style="width: 300px; margin-right: 10px"
        :disabled="isSupplierDisabled"
        >
          <el-option
            v-for="item in optionssupply"
            :key="item.supplierId"
            :label="item.supplierName"
            :value="item.supplierId"   
          />
        </el-select>

        </el-form-item>
        <el-form-item  v-if="false" label="所属机构" prop="goodsBelongOrganId">
          <el-select clearable filterable 
        reserve-keyword  placeholder="请选择所属机构" 
        v-model="selectedValueorgans" style="width: 300px; margin-right: 10px">
          <el-option
            v-for="item in optionsorgans"
            :key="item.organId"
            :label="item.organName"
            :value="item.organId"
          />
        </el-select>

        </el-form-item>
        <el-form-item label="商品介绍" prop="goodsIntro">
          <el-input style="width: 300px" type="textarea" v-model="state.goodForm.goodsIntro" placeholder="请输入商品介绍"></el-input>
        </el-form-item>
        <!-- <el-form-item label="商品价格" prop="originalPrice">
          <el-input type="number" min="0" style="width: 300px" v-model="state.goodForm.originalPrice" placeholder="请输入商品价格"></el-input>
        </el-form-item>
        <el-form-item label="商品售卖价" prop="sellingPrice">
          <el-input type="number" min="0" style="width: 300px" v-model="state.goodForm.sellingPrice" placeholder="请输入商品售价"></el-input>
        </el-form-item>
        <el-form-item label="商品库存" prop="stockNum">
          <el-input type="number" min="0" style="width: 300px" v-model="state.goodForm.stockNum" placeholder="请输入商品库存"></el-input>
        </el-form-item>
        <el-form-item label="商品标签" prop="tag">
          <el-input style="width: 300px" v-model="state.goodForm.tag" placeholder="请输入商品小标签"></el-input>
        </el-form-item>
        <el-form-item label="上架状态" prop="goodsSellStatus">
          <el-radio-group v-model="state.goodForm.goodsSellStatus">
            <el-radio label="0">上架</el-radio>
            <el-radio label="1">下架</el-radio>
          </el-radio-group>
        </el-form-item> -->
        <el-form-item required label="商品封面图" prop="goodsCoverImg"
        
        :rules="[{ 
    validator: (_, val, cb) => val.length > 0 ? cb() : cb(new Error('至少上传1张图片'))
  }]"
        >
          <el-upload
            class="avatar-uploader"
             accept="image/*"
            :action="state.uploadImgServer"
            :headers="{
              token: state.token
            }"
            :show-file-list="false"
            :before-upload="handleBeforeUpload"
            :on-change="handleUrlSuccess"
             :auto-upload="false"
          >
            <img style="width: 100px; height: 100px; border: 1px solid #e9e9e9;" v-if="state.goodForm.goodsCoverImg" :src="state.goodForm.goodsCoverImg" class="avatar">
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item>

        <el-text 
  :truncated="true" 
  :line-clamp="2" 
  type="primary" 
  size="medium"
  style="margin-left: 30px;"
>
建议尺寸：800px*800px，上传小于500kb的图片
</el-text>

<!-- <el-form-item required label="商品轮播图" prop="goodsCoverImg">
          <el-upload
            class="avatar-uploader"
            :action="state.uploadImgServer"
            
      :multiple="true"
     
            accept="jpg,jpeg,png"
            :headers="{
              token: state.token
            }"
           
            :before-upload="handleBeforeUpload"
            :on-success="handleUrlSuccess"
            :on-remove="handleRemove"
          >
            <img style="width: 100px; height: 100px; border: 1px solid #e9e9e9;" v-if="state.goodForm.goodsCoverImg" :src="state.goodForm.goodsCoverImg" class="avatar">
            <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
          </el-upload>
        </el-form-item> -->

        
  <el-form-item required label="商品轮播图" prop="goodsCarousel"
  :rules="[{ 
    validator: (_, val, cb) => val.length > 0 ? cb() : cb(new Error('至少上传1张图片'))
  }]"
  >
    <el-upload
    class="avatar-uploader"
    :show-file-list="false"
      :file-list="form.images"
      action="/api/upload"
      :multiple="true"
      :limit="10"
      :headers="{
              token: state.token
            }"
      accept="image/*"
      
      :auto-upload="false"
      :on-change="handleChangepic"
      :on-remove="handleRemove"
    >
      <el-icon  class="avatar-uploader-icon"><Plus /></el-icon>
      
    </el-upload>
    <div v-for="(url, index) in formurl" :key="index" class="preview-item">
    <img :src="url" class="preview-img" />
    <el-icon class="delete-icon" @click="handleRemove(index)">
      <Close />
    </el-icon>
  </div>
    <!-- 下方图片展示 -->
<!-- <div class="preview-container"> -->

<!-- </div> -->
  </el-form-item>




<el-text 
  :truncated="true" 
  :line-clamp="2" 
  type="primary" 
  size="medium"
  style="margin-left: 30px;"
>
建议尺寸：800px*800px，上传小于500kb的图片，最多可上传10张图片
</el-text>
        <!-- <el-form-item label="详情内容">
          <div ref='editor'></div>
        </el-form-item> -->
        <el-form-item>
          <!-- <el-button type="primary" @click="submitAdd()">{{ state.id ? '立即修改' : '立即创建' }}</el-button> -->
          <el-button type="primary" @click="go()">下一步</el-button>

        </el-form-item>
      </el-form>
    </el-card>


    <el-card v-show="activeName === '2'" class="add-container">

      <el-form :model="state.goodForm" :rules="state.rules" ref="goodRef" label-width="100px" class="goodForm">
      <el-form-item label="商品规格" prop="goodsspecs">
          <el-radio-group v-model="state.goodForm.goodsspecs">
            <el-radio label="0">单规格</el-radio>
            <el-radio label="1">多规格</el-radio>
            <!-- <el-radio label="1">下架</el-radio> -->
          </el-radio-group>
        </el-form-item>
        <el-text 
  :truncated="true" 
  :line-clamp="2" 
  type="primary" 
  size="medium"
  style="margin-left: 30px;"
>
价格设置范围 0.01~999999.99
</el-text>
<el-form-item v-if="state.goodForm.goodsspecs === '1'" label="选择规格" prop="selectedSpecs">
  <el-select clearable filterable 
    reserve-keyword placeholder="请选择规格" 
    @change="handleMainChange"
    v-model="selectedSpecs" 
    style="width: 300px; margin-right: 10px">
    <el-option
      v-for="(_, key) in optionsSpecs[0]"
      :key="key"
      :label="key"
      :value="key"
    />
  </el-select>
</el-form-item>

<!-- Moved edit-item section here, after the select -->
<el-form-item v-if="state.goodForm.goodsspecs === '1'&& Object.keys(currentSubItems).length > 0" label="规格值">
  <div class="specs-container">
    <div v-for="(item, index) in currentSubItems" :key="index" class="spec-item">
      <el-form-item 
        :label="index"
        :prop="'editItems.' + index + '.value'"
        class="spec-form-item"
      >
        <div v-for="(itemx, idx) in item" :key="idx" class="input-group">
          <el-input
            v-model="itemx.attribute"
            placeholder="请输入规格值"
            style="width: 120px; margin-right: 10px;"
            @input="(val) => watchInputValue(index, idx, val)"
          />
          <el-button 
            type="danger" 
            size="small"
            @click="removeEditItem(index, idx)"
            :disabled="itemx.length <= 1"
            style="margin-right: 10px;"
            icon="Delete"
          >
            移除
          </el-button>
        </div>
        <el-button 
          type="primary" 
          size="small"
          style="margin-bottom: 10px;"
          @click="addEditItem(index)"
          icon="Plus"
        >
          添加
        </el-button>
      </el-form-item>
    </div>
  </div>
</el-form-item>
      
  <el-button 
v-if="state.goodForm.goodsspecs === '1'&& Object.keys(currentSubItems).length > 0" 
      type="primary" 
      @click="addItem"
      icon="Plus"
    >
    增加规格
    </el-button>
    <div v-for="(item, index) in formItems" :key="item.id" class="form-item">
      <div  class="edit-mode">
        <span style="margin-top: 2px;">规格名称</span>
        <el-input
        style="width: 300px;"
          v-model="item.nameSpec"
          placeholder="请输入规格名称"
        />
        <span style="margin-top: 2px;">规格值</span>
        <div class="spec-values-wrapper">
          <div v-for="(spec, specIndex) in item.attrSpecs" :key="specIndex" class="spec-value-item">
            <el-input
              style="width: 300px;"
              v-model="spec.value"
              placeholder="请输入规格值"
            />
            <el-button 
              type="danger" 
              size="small"
              @click="removeSpecValue(index, specIndex)"
              :disabled="item.attrSpecs.length <= 1"
              icon="Delete"
            >移除</el-button>
          </div>
        </div>
        <div class="button-group">
          <el-button 
            type="primary" 
            size="small"
            @click="addSpecValue(index)"
            icon="Plus"
            style="height: 32px;"
          >添加</el-button>
          <el-button 
            type="primary" 
            @click="confirmEdit(index)"
          >
            确定
          </el-button>
        </div>
      </div>
      
      <!-- <div  class="view-mode">
        <el-input
        style="width: 300px;"
          v-model="attrSpecs"
          placeholder="请输入规格值"
          
          
        />
        <div class="action-buttons">
     
          <el-button 
            type="danger" 
            @click="removeItem(index)"
            icon="Delete"
          >移除</el-button>
          <el-button 
            type="primary" 
            @click="addItem"
            icon="Plus"
          >添加</el-button>
        </div>
      </div> -->
    </div>
<el-table
    :data="tableData"
    style="width: 71%;margin-left: 30px;"
    border
    highlight-current-row
  >
    <!-- 图片列（按钮） -->
    <el-table-column label="图片" width="120">
      <template #default="{ row, $index }">
        <el-upload
          class="avatar-uploader"
          :action="state.uploadImgServer"
          accept="image/*"
          :headers="{
            token: state.token
          }"
          :auto-upload="false"
          :show-file-list="false"
          :before-upload="handleBeforeUpload"
          :on-change="(file) => handleUrlSuccessSkuForRow(file, row, $index)"
        >
          <img 
            style="width: 100px; height: 100px; border: 1px solid #e9e9e9;" 
            v-if="row.goodsSkuImg" 
            :src="row.goodsSkuImg" 
            class="avatar"
          >
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload>
      </template>
    </el-table-column>

    <!-- 售价列（数字编辑） -->
    <el-table-column label="售价（元）" prop="goodsSellingPrice" width="150">
      <template #default="{ row, $index }">
        <el-input-number
        style="width: 100px;margin-left: 10px;" 
          v-if="activeEdit.index === $index && activeEdit.field === 'goodsSellingPrice'"
          v-model.number="row.goodsSellingPrice"
          :min="0"
          :step="1"
          size="small"
          @blur="saveEdit($index)"
        />
        <span v-else @click="startEdit($index, 'goodsSellingPrice')">
          {{ row.goodsSellingPrice || 0 }}
        </span>
      </template>
    </el-table-column>

    <!-- 成本价列（数字编辑） -->
    <el-table-column label="成本价（元）" prop="goodsCostPrice" width="150">
      <template #default="{ row, $index }">
        <el-input-number
        style="width: 100px;margin-left: 10px;" 
          v-if="activeEdit.index === $index && activeEdit.field === 'goodsCostPrice'"
          v-model.number="row.goodsCostPrice"
          :min="0"
          :step="1"
          size="small"
          @blur="saveEdit($index)"
        />
        <span v-else @click="startEdit($index, 'goodsCostPrice')">
          {{ row.goodsCostPrice || 0 }}
        </span>
      </template>
    </el-table-column>

    <!-- 原价列（数字编辑） -->
    <el-table-column label="原价（元）" prop="goodsOriginalPrice" width="150">
      <template #default="{ row, $index }">
        <el-input-number
        style="width: 100px;margin-left: 10px;" 
          v-if="activeEdit.index === $index && activeEdit.field === 'goodsOriginalPrice'"
          v-model.number="row.goodsOriginalPrice"
          :min="0"
          :step="1"
          size="small"
          @blur="saveEdit($index)"
        />
        <span v-else @click="startEdit($index, 'goodsOriginalPrice')">
          {{ row.goodsOriginalPrice || 0 }}
        </span>
      </template>
    </el-table-column>

    <!-- 库存列（数字编辑） -->
    <el-table-column label="库存" prop="goodsStockNum" width="120">
      <template #default="{ row, $index }">
        <el-input-number
        style="width: 100px;margin-left: 10px;" 
          v-if="activeEdit.index === $index && activeEdit.field === 'goodsStockNum'"
          v-model.number="row.goodsStockNum"
          :min="0"
          :step="1"
          size="small"
          @blur="saveEdit($index)"
        />
        <span v-else @click="startEdit($index, 'goodsStockNum')">
          {{ row.goodsStockNum || 0 }}
        </span>
      </template>
    </el-table-column>

    <!-- 库存预警列（开关编辑） -->
    <el-table-column label="库存预警" prop="goodsStockWarn" width="120">
      <template #default="{ row, $index }">
        <el-input-number
        style="width: 100px;margin-left: 10px;" 
          v-if="activeEdit.index === $index && activeEdit.field === 'goodsStockWarn'"
          v-model.number="row.goodsStockWarn"
          :min="0"
          :step="1"
          size="small"
          @blur="saveEdit($index)"
        />
        <span v-else @click="startEdit($index, 'goodsStockWarn')">
          {{ row.goodsStockWarn || 0 }}
        </span>
      </template>
    </el-table-column>

    <!-- 自定义规格列（文本编辑） -->
    <el-table-column v-if="categoryTypex===0&&state.goodForm.goodsspecs === '0'"  prop="goodsSpecification">
      <template #header="{ column }">
    <!-- 可编辑表头区域 -->
    <el-input
      v-if="headerEdit.active"
      v-model="headerLabel"
      size="small"
      @blur="saveHeaderEdit"
    />
    <span v-else @click="startHeaderEdit">
      {{ headerLabel || '点击编辑表头' }}
    </span>
  </template>
      <template #default="{ row, $index }">
        <el-input
          v-if="activeEdit.index === $index && activeEdit.field === 'goodsSpe'"
          v-model="row.goodsSpe"
          size="small"
          @blur="saveEdit($index)"
        />
        <span v-else @click="startEdit($index, 'goodsSpe')">
          {{ row.goodsSpe || '点击编辑规格' }}
        </span>
      </template>
    </el-table-column>
    <el-table-column v-if="categoryTypex===1||state.goodForm.goodsspecs === '1'"  label="重量(kg)" prop="goodsWeight" width="120">
      <template #default="{ row, $index }">
        <el-input-number
        style="width: 100px;margin-left: 10px;" 
          v-if="activeEdit.index === $index && activeEdit.field === 'goodsWeight'"
          v-model.number="row.goodsWeight"
          :min="0"
          :step="1"
          size="small"
          @blur="saveEdit($index)"
        />
        <span v-else @click="startEdit($index, 'goodsWeight')">
          {{ row.goodsWeight || 0 }}
        </span>
      </template>
    </el-table-column>
    <el-table-column v-if="categoryTypex===1||state.goodForm.goodsspecs === '1'"  label="体积(平方)" prop="goodsVolume" width="120">
      <template #default="{ row, $index }">
        <el-input-number
        style="width: 100px;margin-left: 10px;" 
          v-if="activeEdit.index === $index && activeEdit.field === 'goodsVolume'"
          v-model.number="row.goodsVolume"
          :min="0"
          :step="1"
          size="small"
          @blur="saveEdit($index)"
        />
        <span v-else @click="startEdit($index, 'goodsVolume')">
          {{ row.goodsVolume || 0 }}
        </span>
      </template>
    </el-table-column>
    <el-table-column v-if="state.goodForm.goodsspecs === '1'"  label="操作" prop="goodsVolume" width="120">
      <template #default="{ row, $index }">
        <!-- <el-input-number
        style="width: 100px;margin-left: 10px;" 
          v-if="activeEdit.index === $index && activeEdit.field === 'goodsVolume'"
          v-model.number="row.goodsVolume"
          :min="0"
          :step="1"
          size="small"
          @blur="saveEdit($index)"
        /> -->
        <span   @click="startEditAll( $index, 'goodsSpec')">
          批量编辑
        </span>
      </template>
    </el-table-column>
  </el-table>


  <el-table
  v-if="state.goodForm.goodsspecs === '1'&& Object.keys(currentSubItems).length > 0"
    :data="tableDataSubItems"
    style="margin-left: 30px;"
    border
    highlight-current-row
  >

  <el-table-column 
        v-for="(items, key) in currentSubItems" 
        :key="key"
        :label="key"
        :prop="key"
        :min-width="120"
      >
        <template #header="{ column }">
          <div class="column-header">
            <span>{{ column.label }}</span>
            <el-tooltip 
              :content="`共 ${currentSubItems[key].length} 项`" 
              placement="top"
            >
              <el-icon><InfoFilled /></el-icon>
            </el-tooltip>
          </div>
        </template>
        
        <template #default="{ row, column }">
          <div class="cell-content">
            <span>{{ row[column.property] }}</span>
            <!-- <el-tag 
              size="small" 
              type="info"
              class="id-tag"
            >
              
            </el-tag> -->
          </div>
        </template>
      </el-table-column>


    <!-- 图片列（按钮） -->
    <el-table-column label="图片" width="120">
      <template #default="{ row, $index }">
        <el-upload
          class="avatar-uploader"
          :action="state.uploadImgServer"
          accept="image/*"
          :headers="{
            token: state.token
          }"
          :auto-upload="false"
          :show-file-list="false"
          :before-upload="handleBeforeUpload"
          :on-change="(file) => handleUrlSuccessSkuForRow(file, row, $index)"
        >
          <img 
            style="width: 100px; height: 100px; border: 1px solid #e9e9e9;" 
            v-if="row.goodsSkuImg" 
            :src="row.goodsSkuImg" 
            class="avatar"
          >
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload>
      </template>
    </el-table-column>

    <!-- 售价列（数字编辑） -->
    <el-table-column label="售价（元）" prop="goodsSellingPrice" width="150">
      <template #default="{ row, $index }">
        <el-input-number
        style="width: 100px;margin-left: 10px;" 
          v-if="activeEdit.index === $index && activeEdit.field === 'goodsSellingPrice'"
          v-model.number="row.goodsSellingPrice"
          :min="0"
          :step="1"
          size="small"
          @blur="saveEdit($index)"
        />
        <span v-else @click="startEdit($index, 'goodsSellingPrice')">
          {{ row.goodsSellingPrice || 0 }}
        </span>
      </template>
    </el-table-column>

    <!-- 成本价列（数字编辑） -->
    <el-table-column label="成本价（元）" prop="goodsCostPrice" width="150">
      <template #default="{ row, $index }">
        <el-input-number
        style="width: 100px;margin-left: 10px;" 
          v-if="activeEdit.index === $index && activeEdit.field === 'goodsCostPrice'"
          v-model.number="row.goodsCostPrice"
          :min="0"
          :step="1"
          size="small"
          @blur="saveEdit($index)"
        />
        <span v-else @click="startEdit($index, 'goodsCostPrice')">
          {{ row.goodsCostPrice || 0 }}
        </span>
      </template>
    </el-table-column>

    <!-- 原价列（数字编辑） -->
    <el-table-column label="原价（元）" prop="goodsOriginalPrice" width="150">
      <template #default="{ row, $index }">
        <el-input-number
        style="width: 100px;margin-left: 10px;" 
          v-if="activeEdit.index === $index && activeEdit.field === 'goodsOriginalPrice'"
          v-model.number="row.goodsOriginalPrice"
          :min="0"
          :step="1"
          size="small"
          @blur="saveEdit($index)"
        />
        <span v-else @click="startEdit($index, 'goodsOriginalPrice')">
          {{ row.goodsOriginalPrice || 0 }}
        </span>
      </template>
    </el-table-column>

    <!-- 库存列（数字编辑） -->
    <el-table-column label="库存" prop="goodsStockNum" width="120">
      <template #default="{ row, $index }">
        <el-input-number
        style="width: 100px;margin-left: 10px;" 
          v-if="activeEdit.index === $index && activeEdit.field === 'goodsStockNum'"
          v-model.number="row.goodsStockNum"
          :min="0"
          :step="1"
          size="small"
          @blur="saveEdit($index)"
        />
        <span v-else @click="startEdit($index, 'goodsStockNum')">
          {{ row.goodsStockNum || 0 }}
        </span>
      </template>
    </el-table-column>

    <!-- 库存预警列（开关编辑） -->
    <el-table-column label="库存预警" prop="goodsStockWarn" width="120">
      <template #default="{ row, $index }">
        <el-input-number
        style="width: 100px;margin-left: 10px;" 
          v-if="activeEdit.index === $index && activeEdit.field === 'goodsStockWarn'"
          v-model.number="row.goodsStockWarn"
          :min="0"
          :step="1"
          size="small"
          @blur="saveEdit($index)"
        />
        <span v-else @click="startEdit($index, 'goodsStockWarn')">
          {{ row.goodsStockWarn || 0 }}
        </span>
      </template>
    </el-table-column>

    <!-- 自定义规格列（文本编辑） -->
    <el-table-column v-if="categoryTypex===0&&state.goodForm.goodsspecs === '0'"  prop="goodsSpecification">
      <template #header="{ column }">
    <!-- 可编辑表头区域 -->
    <el-input
      v-if="headerEdit.active"
      v-model="headerLabel"
      size="small"
      @blur="saveHeaderEdit"
    />
    <span v-else @click="startHeaderEdit">
      {{ headerLabel || '点击编辑表头' }}
    </span>
  </template>
      <template #default="{ row, $index }">
        <el-input
          v-if="activeEdit.index === $index && activeEdit.field === 'goodsSpe'"
          v-model="row.goodsSpe"
          size="small"
          @blur="saveEdit($index)"
        />
        <span v-else @click="startEdit($index, 'goodsSpe')">
          {{ row.goodsSpe || '点击编辑规格' }}
        </span>
      </template>
    </el-table-column>
    <el-table-column v-if="categoryTypex===1||state.goodForm.goodsspecs === '1'"  label="重量(kg)" prop="goodsWeight" width="120">
      <template #default="{ row, $index }">
        <el-input-number
        style="width: 100px;margin-left: 10px;" 
          v-if="activeEdit.index === $index && activeEdit.field === 'goodsWeight'"
          v-model.number="row.goodsWeight"
          :min="0"
          :step="1"
          size="small"
          @blur="saveEdit($index)"
        />
        <span v-else @click="startEdit($index, 'goodsWeight')">
          {{ row.goodsWeight || 0 }}
        </span>
      </template>
    </el-table-column>
    <el-table-column v-if="categoryTypex===1||state.goodForm.goodsspecs === '1'"  label="体积(平方)" prop="goodsVolume" width="120">
      <template #default="{ row, $index }">
        <el-input-number
        style="width: 100px;margin-left: 10px;" 
          v-if="activeEdit.index === $index && activeEdit.field === 'goodsVolume'"
          v-model.number="row.goodsVolume"
          :min="0"
          :step="1"
          size="small"
          @blur="saveEdit($index)"
        />
        <span v-else @click="startEdit($index, 'goodsVolume')">
          {{ row.goodsVolume || 0 }}
        </span>
      </template>
    </el-table-column>
    <el-table-column v-if="state.goodForm.goodsspecs === '1'"  label="操作" prop="goodsVolume" width="120">
      <template #default="{ row, $index }">
        <!-- <el-input-number
        style="width: 100px;margin-left: 10px;" 
          v-if="activeEdit.index === $index && activeEdit.field === 'goodsVolume'"
          v-model.number="row.goodsVolume"
          :min="0"
          :step="1"
          size="small"
          @blur="saveEdit($index)"
        /> -->
        <span   @click="handleDelete(row, $index)">
          删除
        </span>
      </template>
    </el-table-column>
  </el-table>





<el-form-item label="商品销量" prop="goodsVirtualSellCount">
          <el-input style="width: 400px" v-model="state.goodForm.goodsVirtualSellCount" placeholder="
  可设置虚拟销量，总销量-虚拟销量=实际销量"></el-input>
        </el-form-item>

        <el-form-item label="详情内容" prop="goodsDetailContent">
          <div ref='editor'></div>
        </el-form-item>
        <el-form-item label="是否核销" prop="goodsNeedWriteOff">
          <el-radio-group v-model="state.goodForm.goodsNeedWriteOff">
            <el-radio label="0">否</el-radio>
            <el-radio label="1">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="false" label="是否上架" prop="goodsSellStatus">
          <el-radio-group v-model="state.goodForm.goodsSellStatus">
            <el-radio label="2">否</el-radio>
            <el-radio label="1">是</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item>
          <!-- <el-button type="primary" @click="submitAdd()">{{ state.id ? '立即修改' : '立即创建' }}</el-button> -->
          <el-button v-if="false" type="primary" style="margin-right: 10px;" @click="back()">上一步</el-button>
          <el-button style="margin-right: 20px;"  v-if="option!=='preview'" type="primary" @click="submitAdd(5)">提交审核并上架</el-button>
          <el-button style="margin-right: 20px;"  v-if="option!=='preview'" type="default" @click="submitAdd(2)">保存草稿</el-button>
          <!-- <el-button  v-if="option!=='preview'" type="default" @click="submitAdd()">取消</el-button> -->

        </el-form-item>
      </el-form>

    </el-card>
  </div>
</template>

<script setup>
import { reactive, ref, onMounted, onBeforeUnmount, getCurrentInstance,computed ,watch } from 'vue'
import WangEditor from 'wangeditor'
import axios from '@/utils/axios'
import { ElMessage,ElMessageBox  } from 'element-plus'
import { useRoute, useRouter } from 'vue-router'
import { localGet, uploadImgServer, uploadImgsServer } from '@/utils'

const { proxy } = getCurrentInstance()
const editor = ref(null)
const goodRef = ref(null)
const route = useRoute()
const router = useRouter()
const { id,option } = route.query
const selectedId = ref([0]); // 默认选中「自营」
const activeName = ref('1');

// 获取组件实例
const cascaderRef = ref();

const categoryTypex = ref(0)
const categoryName =ref('')


const options = [
  { value: 1, label: '自营' },
  { value: 2, label: '第三方' }
];

const optionList = ref([])
const selectedValue = ref('')

const optionsmerchants = ref([]);
const selectedValuemerchants = ref('');
const isMerchantDisabled = ref(false); // New reactive variable

const optionsstore = ref([]);
// const selectedValuestore = ref('');
const selectedValueStore = ref('');
const selectedValueSupply = ref('');

const optionssupply = ref([]);
// const selectedValuesupply = ref('');

const isSupplierDisabled = ref(false); // New reactive variable

const optionsSpecs = ref([]);
const selectedSpecs = ref('');
const currentSubItems = ref({});

const tableDataSubItems = ref([])

const selectedValuecategory = ref([]);


const optionsorgans = ref([]);
const selectedValueorgans = ref('');

const nameSpecs = ref('');
const attrSpecs = ref('');

const formRef = ref(null)

const formItems = ref([])

const addItem = () => {
  if (formItems.value.length > 0) {
    // ElMessage.warning('只能添加一个规格');
    return;
  }
  formItems.value.push({
    id: Date.now(),
    nameSpec: '', // 独立的规格名称
    attrSpecs: [{ value: '' }], // 独立的规格值数组
    isEditing: true
  })
}

const removeItem = (index) => {
  formItems.value.splice(index, 1)
}

const confirmEdit = (index) => {
  const currentItem = formItems.value[index];
  
  // 验证规格名称
  if (!currentItem.nameSpec || !currentItem.nameSpec.trim()) {
    ElMessage.warning('规格名称不能为空');
    return;
  }

  // 验证规格值
  const validSpecValues = currentItem.attrSpecs
    .map(spec => spec.value.trim())
    .filter(value => value !== '');

  if (validSpecValues.length === 0) {
    ElMessage.warning('至少需要一个有效的规格值');
    return;
  }

  // 检查规格值是否在当前规格类型内重复
  const hasDuplicate = validSpecValues.some((value, i) => 
    validSpecValues.indexOf(value) !== i
  );

  if (hasDuplicate) {
    ElMessage.warning('规格值不能重复');
    return;
  }

  // 检查规格值是否与currentSubItems中已有的值重复
  const specName = currentItem.nameSpec.trim();
  const existingValues = currentSubItems.value[specName]?.map(item => item.attribute) || [];
  
  const hasExistingDuplicate = validSpecValues.some(value => 
    existingValues.includes(value)
  );

  if (hasExistingDuplicate) {
    ElMessage.warning('规格值不能与已有的规格值重复');
    return;
  }

  formItems.value[index].isEditing = false;

  // 创建规格项
  if (!currentSubItems.value[specName]) {
    currentSubItems.value[specName] = [];
  }

  // 添加所有有效的规格值
  validSpecValues.forEach(value => {
    currentSubItems.value[specName].push({
      id: 0,
      attribute: value,
      sequence: 0
    });
  });

  // 生成新的组合
  const keys = Object.keys(currentSubItems.value);
  const values = keys.map(key => 
    currentSubItems.value[key].map(item => item.attribute)
  );

  // Generate all combinations
  const generateCombinations = (arrays, current = {}, index = 0) => {
    if (index === arrays.length) {
      return [current];
    }

    const key = keys[index];
    const values = arrays[index];
    const combinations = [];

    for (const value of values) {
      combinations.push(...generateCombinations(arrays, { ...current, [key]: value }, index + 1));
    }

    return combinations;
  };

  // Update tableDataSubItems with all combinations
  tableDataSubItems.value = generateCombinations(values);

  // 清除当前项的输入值
  currentItem.nameSpec = '';
  currentItem.attrSpecs = [{ value: '' }];
};

// // 方法1：添加单个新数据
// const addNewItem = (key, newItem) => {
//   if (!currentSubItems.value[key]) {
//     // 如果键不存在，创建新数组
//     currentSubItems.value[key] = []
//   }
//   currentSubItems.value[key].push(newItem)

//   console.log( currentSubItems.value,' currentSubItems.value')

// // Generate all possible combinations
// const keys = Object.keys(currentSubItems.value);
//   const values = keys.map(key => 
//     currentSubItems.value[key].map(item => item.attribute)
//   );

//   // Generate all combinations
//   const generateCombinations = (arrays, current = {}, index = 0) => {
//     if (index === arrays.length) {
//       return [current];
//     }

//     const key = keys[index];
//     const values = arrays[index];
//     const combinations = [];

//     for (const value of values) {
//       combinations.push(...generateCombinations(arrays, { ...current, [key]: value }, index + 1));
//     }

//     return combinations;
//   };

//   // Update tableDataSubItems with all combinations
//   tableDataSubItems.value = generateCombinations(values);

// }

// 表单数据
const formx = reactive({
  editItems: [
    { value: '' }
  ]
})

const headerEdit = reactive({
  active: false,
});
const headerLabel = ref('自定义规格'); // 初始表头文本

const startHeaderEdit = () => {
  headerEdit.active = true;
};

const saveHeaderEdit = () => {
  headerEdit.active = false;
  // 可在此处添加保存到后端的逻辑:ml-citation{ref="6,7" data="citationList"}
};


// 处理删除
const handleDelete = (row, index) => {
  ElMessageBox.confirm(
    '确定要删除这个组合吗？',
    '警告',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  ).then(() => {
    // 这里可以添加删除逻辑
      // 从 tableDataSubItems 中删除指定索引的数据
    tableDataSubItems.value.splice(index, 1)
    ElMessage.success('删除成功')
  }).catch(() => {})
}

// 表格数据
const tableData = ref([
  {
    imgUrl: '',
    price: 99.99,
    costPrice: 50.00,
    originalPrice: 129.99,
    stock: 100,
    stockAlert: true,
    specs: '标准版'
  }
]);



// 修改 startEdit 函数
const startEditAll = (index, field) => {
  if (field === 'goodsSpec') { // 当点击"批量添加"时
    // 获取当前行的数据
    const currentRow = tableData.value[index];
    
    // 确保当前行有图片数据
    const currentSkuImg = currentRow.goodsSkuImg || state.goodForm.goodsSkuImg;
    
    // 遍历下方表格的所有行，复制数据
    tableDataSubItems.value.forEach((row, idx) => {
      // 复制相关字段的值
      row.goodsSellingPrice = currentRow.goodsSellingPrice || 0;
      row.goodsCostPrice = currentRow.goodsCostPrice || 0;
      row.goodsOriginalPrice = currentRow.goodsOriginalPrice || 0;
      row.goodsStockNum = currentRow.goodsStockNum || 0;
      row.goodsStockWarn = currentRow.goodsStockWarn || 0;
      row.goodsWeight = currentRow.goodsWeight || 0;
      row.goodsVolume = currentRow.goodsVolume || 0;
      // 使用当前行的图片或表单中的图片
      row.goodsSkuImg = currentSkuImg;
    });
    
    // 同时更新表单中的图片，确保数据同步
    state.goodForm.goodsSkuImg = currentSkuImg;
    
    ElMessage.success('批量复制成功');
  } else {
    // 原有的编辑逻辑保持不变
    activeEdit.index = index;
    activeEdit.field = field;
  }
};



// 当前编辑状态
const activeEdit = reactive({
  index: -1,
  field: ''
});

// 开始编辑
const startEdit = (index, field) => {
  activeEdit.index = index;
  activeEdit.field = field;
};

// 保存编辑
const saveEdit = (index) => {
  activeEdit.index = -1;
  activeEdit.field = '';
  // 这里可以添加数据保存逻辑
  // ElMessage.success(`第 ${index + 1} 行数据已更新`);
};

const handleMainChange = () => {
  if (!selectedSpecs.value || !optionsSpecs.value || !optionsSpecs.value[0]) {
    console.warn('Invalid specs data');
    currentSubItems.value = {};
    tableDataSubItems.value = [];
    return;
  }

  const specObject = optionsSpecs.value[0][selectedSpecs.value];
  
  if (!specObject) {
    console.warn('No specification data found for selected spec');
    currentSubItems.value = {};
    tableDataSubItems.value = [];
    return;
  }

  // Update currentSubItems with the spec data
  currentSubItems.value = specObject;

  // Generate combinations directly instead of using computed
  console.log(currentSubItems.value,'currentSubItems.value')

// Generate all possible combinations
const keys = Object.keys(currentSubItems.value);
  const values = keys.map(key => 
    currentSubItems.value[key].map(item => item.attribute)
  );

  // Generate all combinations
  const generateCombinations = (arrays, current = {}, index = 0) => {
    if (index === arrays.length) {
      return [current];
    }

    const key = keys[index];
    const values = arrays[index];
    const combinations = [];

    for (const value of values) {
      combinations.push(...generateCombinations(arrays, { ...current, [key]: value }, index + 1));
    }

    return combinations;
  };

  // Update tableDataSubItems with all combinations
  tableDataSubItems.value = generateCombinations(values);
  console.log(tableDataSubItems.value,'tableDataSubItems.value')
};

const watchInputValue = (index, idx, newValue) => {
  const trimmedValue = newValue ? newValue.trim() : '';

  // Only proceed if the new value is not empty after trimming
  if (trimmedValue) {
    // Check for duplicate values within the same specification type (index)
    const isDuplicate = currentSubItems.value[index].some((item, itemIdx) => {
      // Compare attribute values, excluding the current item being edited (itemIdx === idx)
      return itemIdx !== idx && item.attribute === trimmedValue;
    });

    if (isDuplicate) {
      ElMessage.warning(`规格值 "${trimmedValue}" 已存在，请勿重复添加`);
      return;
    }

    // Update the attribute in currentSubItems
    currentSubItems.value[index][idx].attribute = trimmedValue;

    // 保存当前表格数据
    const currentTableData = [...tableDataSubItems.value];

    // Generate new combinations
    const keys = Object.keys(currentSubItems.value);
    const values = keys.map(key =>
      currentSubItems.value[key].map(item => item.attribute)
    );

    // Filter out any empty attributes before generating combinations
    const validValues = keys.map(key =>
      currentSubItems.value[key]
        .map(item => item.attribute)
        .filter(attr => attr !== '')
    );

    // Check if all specification types have at least one non-empty value
    const allSpecsHaveValues = validValues.every(specValues => specValues.length > 0);

    if (allSpecsHaveValues) {
      // Generate all combinations
      const generateCombinations = (arrays, current = {}, index = 0) => {
        if (index === arrays.length) {
          return [current];
        }

        const key = keys[index];
        const values = arrays[index];
        const combinations = [];

        for (const value of values) {
          combinations.push(...generateCombinations(arrays, { ...current, [key]: value }, index + 1));
        }

        return combinations;
      };

      // 生成新的组合
      const newCombinations = generateCombinations(validValues);

      // 合并新旧数据
      tableDataSubItems.value = newCombinations.map(newRow => {
        // 查找匹配的旧行数据
        const matchingOldRow = currentTableData.find(oldRow => {
          // 检查所有规格值是否匹配
          return keys.every(key => oldRow[key] === newRow[key]);
        });

        if (matchingOldRow) {
          // 如果找到匹配的旧行，保留其所有属性值
          return {
            ...matchingOldRow,  // 保留所有旧数据
            ...newRow  // 更新规格值
          };
        } else {
          // 如果是全新的组合，使用默认值
          return {
            ...newRow,
            goodsSkuImg: '',
            goodsSellingPrice: 0,
            goodsCostPrice: 0,
            goodsOriginalPrice: 0,
            goodsStockNum: 0,
            goodsStockWarn: 0,
            goodsWeight: 0,
            goodsVolume: 0
          };
        }
      });
    } else {
      // 如果任何规格类型为空，清空表格
      tableDataSubItems.value = [];
    }
  } else {
    // 如果新值为空，更新属性为空字符串
    currentSubItems.value[index][idx].attribute = trimmedValue;

    // 重新生成组合，保持相同的数据保留逻辑
    const currentTableData = [...tableDataSubItems.value];
    const keys = Object.keys(currentSubItems.value);
    const values = keys.map(key =>
      currentSubItems.value[key]
        .map(item => item.attribute)
        .filter(attr => attr !== '')
    );

    const allSpecsHaveValues = values.every(specValues => specValues.length > 0);

    if (allSpecsHaveValues) {
      const generateCombinations = (arrays, current = {}, index = 0) => {
        if (index === arrays.length) {
          return [current];
        }

        const key = keys[index];
        const values = arrays[index];
        const combinations = [];

        for (const value of values) {
          combinations.push(...generateCombinations(arrays, { ...current, [key]: value }, index + 1));
        }

        return combinations;
      };

      const newCombinations = generateCombinations(values);
      
      // 使用相同的数据合并逻辑
      tableDataSubItems.value = newCombinations.map(newRow => {
        const matchingOldRow = currentTableData.find(oldRow => {
          return keys.every(key => oldRow[key] === newRow[key]);
        });

        if (matchingOldRow) {
          return {
            ...matchingOldRow,
            ...newRow
          };
        } else {
          return {
            ...newRow,
            goodsSkuImg: '',
            goodsSellingPrice: 0,
            goodsCostPrice: 0,
            goodsOriginalPrice: 0,
            goodsStockNum: 0,
            goodsStockWarn: 0,
            goodsWeight: 0,
            goodsVolume: 0
          };
        }
      });
    } else {
      tableDataSubItems.value = [];
    }
  }
};


// 添加新的编辑框
const addEditItem = (index) => {
  if (!currentSubItems.value[index]) {
    currentSubItems.value[index] = [];
  }
  currentSubItems.value[index].push({
    id: 0,
    attribute: '',
    sequence: 0
  });
};

// 移除指定索引的编辑框
const removeEditItem = (index, idx) => {
  if (currentSubItems.value[index].length > 1) {
    // Remove the item from currentSubItems
    currentSubItems.value[index].splice(idx, 1);

    // Generate new combinations after removal
    const keys = Object.keys(currentSubItems.value);
    const values = keys.map(key => 
      currentSubItems.value[key].map(item => item.attribute)
    );

    // Generate all combinations
    const generateCombinations = (arrays, current = {}, index = 0) => {
      if (index === arrays.length) {
        return [current];
      }

      const key = keys[index];
      const values = arrays[index];
      const combinations = [];

      for (const value of values) {
        combinations.push(...generateCombinations(arrays, { ...current, [key]: value }, index + 1));
      }

      return combinations;
    };

    // Update tableDataSubItems with new combinations
    tableDataSubItems.value = generateCombinations(values);
  } else {
    ElMessage.warning('至少保留一个规格值');
  }
};

// 库存预警状态变更
const handleStockAlertChange = (index) => {
  ElMessage.info(`库存预警状态已更新: ${tableData.value[index].stockAlert ? '开启' : '关闭'}`);
};

// 图片上传处理
const handleImageUpload = (index) => {
  // 这里添加实际图片上传逻辑
  ElMessage.info(`开始上传第 ${index + 1} 行的图片`);
};

const handleTabClick = (tab) => {
  // console.log('当前激活标签:', tab.props.name);
  // if (tab.props.name === '1') {
  //   console.log('当前激活标签:', tab.props.name);
  //   activeName = '1';
  // }else{
  //   console.log('当前激活标签:', tab.props.name);
  //   activeName = '2';
  // }
  // this.$refs.tabs.setCurrentName("1"); // 参数为要切换的tab的name

  activeName.value = tab.props.name;
  console.log('当前激活标签:', activeName);
};

const form = reactive({
  images: [] // 存储原始文件对象
});


const formurl = ref([]); 

const previewList = ref([]); // 存储预览图URL

// 生成Base64预览
const generatePreview = (file) => {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target.result);
    reader.readAsDataURL(file);
  });
};

const handleChangepic = async (file) => {
  // 数量校验
  if (form.images.length >= 10) {
    ElMessage.warning('最多选择10张图片');
    return false;
  }

  // 格式校验
  if (!['image/jpeg', 'image/png'].includes(file.raw?.type || '')) {
    ElMessage.error('仅支持JPG/PNG格式');
    return false;
  }

  // if (!['jpg', 'jpeg', 'png'].includes(file.raw?.type || '')) {
  //   ElMessage.error('请上传 jpg、jpeg、png 格式的图片')
  //   return false
  // }
  // console.log('previewUrl')
  // 生成预览并更新数据
  try {
    const previewUrl = await generatePreview(file.raw);
    // console.log(previewUrl,'previewUrl')
    previewList.value.push(previewUrl);
    form.images.push(file.raw);

    const formData = new FormData();
    formData.append('file', file.raw); // 文件字段
  // form.images.forEach((file, index) => {
  //   formData.append(`images[${index}]`, file);
  // });
  

  axios.post('/upload/file', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }).then(res => {
    // 处理成功逻辑
    // console.log(res, 'res');
    // if(res.resultCode === 200){
      formurl.value.push(res.url);
      // state.ruleForm.merchantLogo = res.url
    // }
  });



  } catch (error) {
    console.error('生成预览失败:', error);
    ElMessage.error('文件读取失败');
  }
};

const handleRemove = (index) => {
  console.log(index,'index')
  previewList.value.splice(index, 1);
  form.images.splice(index, 1);
  formurl.value.splice(index, 1);
  // console.log(previewList.value,'index')
  // console.log(form.images,'index')
  console.log(formurl.value,'index')
};

// const form = reactive({
//   images: [] as File[] // 存储原始文件对象
// });

// const previewList = ref<string[]>([]); // 存储预览图URL

// // 生成Base64预览
// const generatePreview = (file: File) => {
//   return new Promise<string>((resolve) => {
//     const reader = new FileReader();
//     reader.onload = (e) => resolve(e.target?.result as string);
//     reader.readAsDataURL(file);
//   });
// };

// const handleChange = async (file: UploadFile) => {
//   // 数量校验
//   if (form.images.length >= 10) {
//     ElMessage.warning('最多选择10张图片');
//     return false;
//   }

//   // 格式校验
//   if (!['image/jpeg', 'image/png'].includes(file.raw?.type || '')) {
//     ElMessage.error('仅支持JPG/PNG格式');
//     return false;
//   }

//   // 生成预览并更新数据
//   const previewUrl = await generatePreview(file.raw);
//   previewList.value.push(previewUrl);
//   form.images.push(file.raw);
// };

// const handleRemove = (index: number) => {
//   previewList.value.splice(index, 1);
//   form.images.splice(index, 1);
// };

const submitForm = () => {
  const formData = new FormData();
  form.images.forEach((file, index) => {
    formData.append(`images[${index}]`, file);
  });

  axios.post('/api/submit', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }).then(res => {
    // 处理成功逻辑
  });
};


const state = reactive({
  uploadImgServer,
  token: localGet('token') || '',
  id: id,
  defaultCate: '请选择商品分类',
  goodForm: {
    goodsName: '',
    goodsSubName: '',
    goodsIntro: '',
    originalPrice: '',
    sellingPrice: '',
    stockNum: '',
    goodsSellStatus: '2',
    goodsCoverImg: '',
    goodsSkuImg: '',
    goodsspecs:'0',
    goodsVirtualSellCount: '',
    goodsNeedWriteOff:'',
    goodsType:'',
    tag: ''
  },
  rules: {
    goodsName: [
      { required: 'true', message: '请填写商品名称', trigger: ['change'] }
    ],
    originalPrice: [
      { required: 'true', message: '请填写商品价格', trigger: ['change'] }
    ],
    sellingPrice: [
      { required: 'true', message: '请填写商品售价', trigger: ['change'] }
    ],
    stockNum: [
      { required: 'true', message: '请填写商品库存', trigger: ['change'] }
    ],
    defaultCate: [
    { type: 'array', required: true, message: '请选择商品分类', trigger: 'change' }
  ],
  // 修改商品分类验证规则
  // categoryId: [
  //     { 
  //       required: true, 
  //       message: '请选择商品分类', 
  //       trigger: 'change',
  //       validator: (rule, value, callback) => {
  //         if (!state.categoryId) {
  //           callback(new Error('请选择商品分类'))
  //         } else {
  //           callback()
  //         }
  //       }
  //     }
  //   ],
    
    // 修改所属类型验证规则
    selectedId: [
      { 
        required: true, 
        message: '请选择所属类型', 
        trigger: 'change',
        validator: (rule, value, callback) => {
          if (!selectedId.value) {
            callback(new Error('请选择所属类型'))
          } else {
            callback()
          }
        }
      }
    ],
    
    // 修改所属商户验证规则
    goodsBelongMerchantId: [
      { 
        required: true, 
        message: '请选择所属商户', 
        trigger: 'change',
        validator: (rule, value, callback) => {
          if (!selectedValuemerchants.value) {
            callback(new Error('请选择所属商户'))
          } else {
            callback()
          }
        }
      }
    ],
    goodsBelongShopId: [
    { 
      required: true, 
      message: '请选择所属店铺', 
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (!selectedValueStore.value) {
          callback(new Error('请选择所属店铺'));
        } else {
          callback();
        }
      }
    }
  ],
  goodsSupplierId: [
    { 
      required: true, 
      message: '请选择所属供应商', 
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (!selectedValueSupply.value) {
          callback(new Error('请选择所属供应商'));
        } else {
          callback();
        }
      }
    }
  ],
  // defaultCates: [
  //   { type: 'array', required: true, message: '请选择所属类型', trigger: 'change' }
  // ],
  // selectedId: [
  //   {  required: true, 
  //     message: '请选择商品归属', 
  //     trigger: ['change', 'blur'],   }
  // ],
  selectedValue: [
    { type: 'array', required: true, message: '请选择所属机构', trigger: 'change' }
  ],
  selectedSpecs: [
    { 
      required: true, 
      message: '请选择规格', 
      trigger: 'change',
      validator: (rule, value, callback) => {
        if (state.goodForm.goodsspecs === '1' && !selectedSpecs.value) {
          callback(new Error('请选择规格'))
        } else {
          callback()
        }
      }
    }
  ],
  },
  
  categoryId: '',
  category: {
    lazy: true,
    lazyLoad(node, resolve) {
      const { level = 0, value } = node
      if (level === 0) {
        axios.get('/categories', {
        params: {
          pageNumber: 1,
          pageSize: 1000,
          
        }
      }).then(res => {
        const list = res.list
        const nodes = list.map(item => ({
          value: item.categoryId,
          label: item.categoryName,
          categoryType:item.categoryType,
          // leaf: level > 1
          leaf: item.childrenList===null
        }))
        resolve(nodes)
      })
      } else if (level === 1) {
        axios.get('/categoryList', {
        params: {
          pageNumber: 1,
          pageSize: 1000,
          categoryLevel: level + 1,
          parentId: value || 0
        }
      }).then(res => {
        const list = res.list
        const nodes = list.map(item => ({
          value: item.categoryId,
          label: item.categoryName,
          categoryType:item.categoryType,
          // leaf: level > 1
          leaf: true
        }))
        resolve(nodes)
      })
      }
     
    }
  }
})
let instance
onMounted(() => {
  console.log('option', option)
  instance = new WangEditor(editor.value)
  instance.config.showLinkImg = false
  instance.config.showLinkImgAlt = false
  instance.config.showLinkImgHref = false
  instance.config.uploadImgMaxSize = 2 * 1024 * 1024 // 2M
  instance.config.uploadFileName = 'file'
  instance.config.uploadImgHeaders = {
    token: state.token
  }
  
  // 修改图片上传配置
  instance.config.uploadImgServer = uploadImgsServer // 使用与 el-upload 相同的上传地址
  
  // 自定义图片上传
  instance.config.uploadImgHooks = {
    // 上传图片之前
    before: function(file) {
      // 添加文件存在性检查
      // if (!file || !file.name) {
      //   ElMessage.error('无效的文件1')
      //   return false
      // }

      // 检查文件类型
      // const sufix = file.name.split('.')[1]?.toLowerCase() || ''
      // if (!['jpg', 'jpeg', 'png'].includes(sufix)) {
      //   ElMessage.error('请上传 jpg、jpeg、png 格式的图片')
      //   return false
      // }

      // // 检查文件大小
      // const maxSize = 2 * 1024 * 1024 // 2MB
      // if (file.size > maxSize) {
      //   ElMessage.error('图片大小不能超过 2MB')
      //   return false
      // }

      // return true
    },
    
    // 上传图片进度条
    customUpload: function(file, insertFn) {
      if (!file) {
        ElMessage.error('无效的文件')
        return
      }

      // 创建 FormData
      const formData = new FormData()
      formData.append('file', file.raw)
      
      // 使用 axios 上传
      axios.post('/upload/file', formData, {
        headers: { 
          'Content-Type': 'multipart/form-data'
      
        }
      }).then(res => {
        console.log('上传响应:', res) // 添加日志查看返回结果
        
        // 直接使用返回的 url
        if (res && res.url) {
          // 直接调用 insertFn 插入图片
          insertFn(res.url)
          ElMessage.success('图片上传成功')
        } else {
          ElMessage.error('图片上传失败：未获取到图片地址')
        }
      }).catch(err => {
        console.error('图片上传失败:', err)
        ElMessage.error('图片上传失败：' + (err.message || '未知错误'))
      })
    },
    
    // 上传图片失败
    fail: function(xhr, editor, resData) {
      ElMessage.error('图片上传失败')
    },
    
    // 上传图片错误
    error: function(xhr, editor, resData) {
      ElMessage.error('图片上传出错')
    },
    
    // 上传图片超时
    timeout: function(xhr, editor, resData) {
      ElMessage.error('图片上传超时')
    },
    
    // 添加 customInsert 钩子
    customInsert: function(insertImgFn, result) {
      // result 即服务端返回的接口
      // console.log('上传响应:', result)
      if (result) {
       insertImgFn(result.data.url)
      }
    }
  }

  // 其他配置保持不变
  Object.assign(instance.config, {
    onchange() {
      console.log('change')
    },
  })
  
  instance.create()
  getSpecs()
  if (id) {
    axios.get(`/goods/${id}`).then(res => {
      const { goods, firstCategory, secondCategory, thirdCategory } = res
      state.goodForm = {
        goodsName: goods.goodsName,
        goodsIntro: goods.goodsIntro,
        // goodsType:String(goods.goodsType),
        goodsNeedWriteOff:String(goods.goodsNeedWriteOff),
        goodsVirtualSellCount:goods.goodsVirtualSellCount,
        goodsSubName:goods.goodsSubName,
        goodsSellStatus: String(goods.goodsSellStatus),
        goodsspecs:goods.goodsMultiSpecification,
        goodsCoverImg: proxy.$filters.prefix(goods.goodsCoverImg),
       
      }
      // tableData.value[0].goodsOriginalPrice = goods.goodsOriginalPrice
      // tableData.value[0].goodsSellingPrice = goods.goodsSellingPrice
      // tableData.value[0].goodsStockNum = goods.goodsStockNum
      // tableData.value[0].goodsCostPrice = goods.goodsCostPrice
      // tableData.value[0].goodsStockWarn = goods.goodsStockWarn

      formurl.value = goods.goodsCarousel.split(',').map(url => url.trim());
      // console.log( formurl.value,'formurl.value')
      // previewList.value.push(goodsCarousel);
      selectedValuemerchants.value =goods.goodsBelongMerchantId
      selectedValueorgans.value=goods.goodsBelongOrganId

      selectedValueStore.value=goods.goodsBelongShopId
      selectedValueSupply.value=goods.goodsBelongSupplierId

      // state.goodForm.goodsspecs = '1'
      state.goodForm.goodsspecs = String(goods.goodsMultiSpecification)
      if(goods.goodsMultiSpecification === 1){
        firstSequence.value = goods.sequence
        console.log('firstSequence.value:', firstSequence.value, typeof firstSequence.value)
        console.log('optionsSpecs.value[0]:', optionsSpecs.value[0])
        const specs = optionsSpecs.value[0];
        if (specs) {
          console.log('Available specs:', Object.keys(specs))
          // Iterate through all specs to find matching sequence
          for (const [specName, specItems] of Object.entries(specs)) {
            console.log('Checking spec:', specName)
            
            // 遍历每个规格类型（如"颜色"、"尺寸"等）
            for (const [attrName, items] of Object.entries(specItems)) {
              // items 是一个数组，包含该规格类型的所有选项
              if (Array.isArray(items)) {
                // 检查这个规格类型下的所有选项
                const hasMatchingSequence = items.some(item => {
                  console.log('Comparing:', item.sequence, firstSequence.value, typeof item.sequence, typeof firstSequence.value)
                  return item.sequence === firstSequence.value
                });
                
                if (hasMatchingSequence) {
                  selectedSpecs.value = specName;
                  console.log('Found matching spec:', specName)
                  break;
                }
              }
            }
            
            // 如果已经找到匹配的规格，跳出外层循环
            if (selectedSpecs.value === specName) {
              break;
            }
          }
        } else {
          console.log('No specs available')
        }
        // Transform the array of objects into a single object
        const mergedSpecs = goods.specAndAttrs.reduce((acc, curr) => {
          return { ...acc, ...curr };
        }, {});
        currentSubItems.value = mergedSpecs;

        // Transform descriptions into tableDataSubItems format
        tableDataSubItems.value = goods.descriptions.map(item => {
          // Create base object with all the product details
          const rowData = {
            goodsSkuImg: item.img || '',
            goodsSellingPrice: item.goodsSellingPrice || 0,
            goodsCostPrice: item.goodsCostPrice || 0,
            goodsOriginalPrice: item.goodsOriginalPrice || 0,
            goodsStockNum: item.goodsStockNum || 0,
            goodsStockWarn: item.goodsStockWarn || 0,
            goodsWeight: item.goodsWeight || 0,
            goodsVolume: item.goodsVolume || 0
          };

          // Add all spec values from attrs array
          item.attrs.forEach(attrObj => {
            // Each attrObj has a single key-value pair
            const [specName, specValue] = Object.entries(attrObj)[0];
            rowData[specName] = specValue;
          });

          return rowData;
        });
      }else{
        tableData.value[0].goodsOriginalPrice = goods.descriptions[0].goodsOriginalPrice
      tableData.value[0].goodsSellingPrice = goods.descriptions[0].goodsSellingPrice
      tableData.value[0].goodsStockNum = goods.descriptions[0].goodsStockNum
      tableData.value[0].goodsCostPrice = goods.descriptions[0].goodsCostPrice
      tableData.value[0].goodsStockWarn = goods.descriptions[0].goodsStockWarn
      tableData.value[0].goodsWeight = goods.goodsWeight
      tableData.value[0].goodsVolume = goods.goodsVolume
      // const goodsSpecificationObject = JSON.parse(goods.goodsSpecification);


      tableData.value[0].goodsSkuImg = goods.descriptions[0].img;
      }
     
      


      // tableData.value[0].goodsWeight = goods.goodsWeight
      // tableData.value[0].goodsVolume = goods.goodsVolume
      
      const goodsSpecificationObject = JSON.parse(goods.goodsSpecification);

      state.goodForm.goodsSkuImg = goodsSpecificationObject.sku;
      
      const parts = String(goodsSpecificationObject.spec).split(':');
      headerLabel.value=  parts[0]?.trim() || ''
      tableData.value[0].goodsSpe=parts[1]?.trim() || ''
      console.log( goodsSpecificationObject.spec,'111111')
      console.log( parts[1]?.trim() || '','222222')
      if(option !== 'copy'){
        state.categoryId = goods.goodsCategoryId
      state.defaultCate = goods.goodsCategoryName
      selectedValuecategory.value = goods.goodsCategoryId
      selectedId.value = goods.goodsBelongType
      }
      if(option === 'edit'){

      }
      // state.goodForm.goodsspecs 
      // state.defaultCate = `${firstCategory.categoryName}/${secondCategory.categoryName}/${thirdCategory.categoryName}`
      if (instance) {
        // 初始化商品详情 html
        instance.txt.html(goods.goodsDetailContent)
      }
    })
  }
  // getOrderList()
  // getOrgans()
  getStore()
  // getSupply()
   // Call getProfile when component mounts
   loadData();
})
const firstSequence = ref(0)



// const getStore = () => {
//   state.loading = true

//   console.log(selectedstateId ,'index')
//   axios.get('/shops', {
//     params: {
//       pageNumber: state.currentPage,
//       pageSize: state.pageSize,
//       shopName: state.shopName,
//       status: selectedstateId.value=== undefined?'':selectedstateId.value.length?'':selectedstateId.value
//     }
//   }).then(res => {
//     state.tableData = res.list
//     state.total = res.totalCount
//     state.currentPage = res.currPage
//     state.loading = false
//   })
// }


const getOrgans = () => {
  // state.loading = true

  // console.log(selectedstateId ,'index')
  axios.get('/organs', {
    params: {
      pageNumber: 1,
      pageSize: 100,
      
    }
  }).then(res => {
    optionsorgans.value = res.list

  })
}

const getOrderList = () => {
  return axios.get('/merchants', {
    params: {
      pageNumber: 1,
      pageSize: 100,
      merchantName: '',
      status: ''
    }
  }).then(res => {
    optionsmerchants.value = res.list;
  });
};

const getSupply = () => {
  return axios.get('/supplier/list', {
    params: {
      pageNumber: 1,
      pageSize: 100,
      merchantName: '',
      status: ''
    }
  }).then(res => {
    optionssupply.value = res.list;
  });
};

const loadData = () => {
  Promise.all([getOrderList(), getSupply()])
    .then(() => {
      if(option === undefined){
      getProfile();
      }
    })
    .catch(error => {
      console.error('Failed to load data:', error);
      ElMessage.error('加载数据失败');
    });
};

// 在 onMounted 中调用 loadData 替代原来的 getOrderList 和 getSupply
// onMounted(() => {
//   loadData();
//   // 其他初始化逻辑...
// });

const getSpecs = () => {
  axios.get('/showSpecifications', {
    params: {
      pageNumber: 1,
      pageSize: 1000,
    }
  }).then(res => {
    optionsSpecs.value = res.list
    // if (id && firstSequence.value) {
    //   // Find the spec that matches firstSequence
    //   const specs = optionsSpecs.value[0];
    //   if (specs) {
    //     // Iterate through all specs to find matching sequence
    //     for (const [specName, specItems] of Object.entries(specs)) {
    //       // Check if any item in this spec has matching sequence
    //       const hasMatchingSequence = specItems.some(item => item.sequence === firstSequence.value);
    //       if (hasMatchingSequence) {
    //         selectedSpecs.value = specName;
    //         // Trigger the change handler to update the UI
    //         // handleMainChange();
    //         break;
    //       }
    //     }
    //   }
    // }
  })
}

const getStore = () => {
  // state.loading = true

  // console.log(selectedstateId ,'index')

  // state.loading = true

  // console.log(selectedstateId ,'index')
  axios.get('/shops', {
    params: {
      pageNumber: 1,
      pageSize: 100,
      shopName: '',
      status: ''
    }
  }).then(res => {
    optionsstore.value = res.list
  })



  // axios.get('/merchants', {
  //   params: {
  //     pageNumber: 1,
  //     pageSize: 100,
  //     merchantName: '',
  //     status: ''
  //   }
  // }).then(res => {
  //   optionsstore.value = res.list

  // })
}

const getProfile = () => {
  axios.get('/adminUser/profile').then(res => {
    console.log('Profile API response:', res);
    console.log('optionsmerchants:', optionsmerchants.value);
    console.log('optionssupply:', optionssupply.value);

    // For merchant matching
    const matchedMerchant = optionsmerchants.value.find(merchant => merchant.merchantId === res.merchantId);
    if (matchedMerchant) {
      isMerchantDisabled.value = true;
      selectedValuemerchants.value = matchedMerchant.merchantId;
      console.log('Matched merchant - ID:', matchedMerchant.merchantId, 'Name:', matchedMerchant.merchantName);
    }

    // For supplier matching
    const matchedSupplier = optionssupply.value.find(supplier => supplier.supplierId === res.supplierId);
    if (matchedSupplier) {
      isSupplierDisabled.value = true;
      selectedValueSupply.value = matchedSupplier.supplierId;
      console.log('Matched supplier - ID:', matchedSupplier.supplierId, 'Name:', matchedSupplier.supplierName);
    }
  }).catch(error => {
    console.error('Failed to fetch profile:', error);
    ElMessage.error('获取用户信息失败');
  });
};


onBeforeUnmount(() => {
  instance.destroy()
  instance = null
})
const go = () => {
  activeName.value = '2';
}
const back = () => {
  activeName.value = '1';
}


const allTableData = ref([])
const arrayFormat = ref([]);  // Initialize as empty array
const firstSequencex = ref(0)



const submitAdd = (status) => {
  // let allTableData = [];
  if(state.goodForm.goodsspecs === '1'){
    allTableData.value = tableDataSubItems.value.map(row => {
      // Create a base object with common fields
      const baseData = {
        goodsSkuImg: row.goodsSkuImg || '',
        goodsSellingPrice: row.goodsSellingPrice || 0,
        goodsCostPrice: row.goodsCostPrice || 0,
        goodsOriginalPrice: row.goodsOriginalPrice || 0,
        goodsStockNum: row.goodsStockNum || 0,
        goodsStockWarn: row.goodsStockWarn || 0,
        goodsWeight: row.goodsWeight || 0,
        goodsVolume: row.goodsVolume || 0,
        attrs: Object.entries(currentSubItems.value).map(([specName, items]) => {
          // Find the matching item for this spec in the current row
          const itemValue = row[specName] || '';
          return {
            [specName]: itemValue
          };
        })
      };
      return baseData;
    });

    console.log(allTableData.value,'allTableData')

    arrayFormat.value = [currentSubItems.value];  // Use .value to modify the ref

    console.log('Array format:', arrayFormat.value);
    firstSequencex.value = Object.values(currentSubItems.value)[0]?.[0]?.sequence || 0;
    console.log('First sequence value:', firstSequencex.value);
  }else{

    const goodsSpeValue = tableData.value[0].goodsSpe || '个';

allTableData.value = [{
  img: tableData.value[0].goodsSkuImg || '',
  goodsSellingPrice: tableData.value[0].goodsSellingPrice || 0,
  goodsCostPrice: tableData.value[0].goodsCostPrice || 0,
  goodsOriginalPrice: tableData.value[0].goodsOriginalPrice || 0,
  goodsStockNum: tableData.value[0].goodsStockNum || 0,
  goodsStockWarn: tableData.value[0].goodsStockWarn || 0,
  goodsWeight: tableData.value[0].goodsWeight || 0,
  goodsVolume: tableData.value[0].goodsVolume || 0,
  attrs: headerLabel.value && goodsSpeValue ? [{
    [headerLabel.value]: goodsSpeValue
  }] : []
}];

// Format custom specification into arrayFormat if title and value exist
if (headerLabel.value && goodsSpeValue) {
  arrayFormat.value = [{
    [headerLabel.value]: [{
      id: 0,
      attribute: goodsSpeValue,
      sequence: 0
    }]
  }];
} else {
  arrayFormat.value = [];
}
}
  

  goodRef.value.validate((vaild) => {
    if (vaild) {


      const selectedMerchant = optionsmerchants.value.find(
        merchant => merchant.merchantId === selectedValuemerchants.value
      );
      const merchantName = selectedMerchant ? selectedMerchant.merchantName : '';
      const goodsSpeValue = tableData.value[0].goodsSpe || '个';

      const myMap = new Map([
  ['sku', state.goodForm.goodsSkuImg],
  ['spec',headerLabel.value+ ': ' +goodsSpeValue]
]);
console.log('headerLabel', headerLabel)
console.log('tableData.value[0].goodsSpe', goodsSpeValue)
// Map 转普通对象
const mapToObject = Object.fromEntries(myMap.entries());

// 转 JSON 字符串
const jsonStr = JSON.stringify(mapToObject);
// 结果: {"name":"John","123":{"age":30}}
      // 默认新增用 post 方法
      let httpOption = axios.post
      let params = {
        goodsCategoryId: state.categoryId,
        goodsCoverImg: state.goodForm.goodsCoverImg,
        goodsCarousels:formurl.value,
        goodsDetailContent: instance.txt.html(),
        goodsIntro: state.goodForm.goodsIntro,
        goodsName: state.goodForm.goodsName,
        goodsSubName:state.goodForm.goodsSubName,
        goodsBelongType:'2',
        // goodsBelongType:selectedId.value=== undefined?'':selectedId.value.length?'':selectedId.value,
        goodsBelongMerchantId:selectedValuemerchants.value,
        goodsBelongMerchantName: merchantName,
        goodsBelongOrganId:selectedValueorgans.value,
        goodsBelongShopId:selectedValueStore.value,
        goodsBelongSupplierId:selectedValueSupply.value,
        // goodsSellStatus: +state.goodForm.goodsSellStatus,
        goodsSellStatus: status,
        goodsOriginalPrice: tableData.value[0].goodsOriginalPrice,
        goodsSellingPrice: tableData.value[0].goodsSellingPrice,
        goodsCostPrice:tableData.value[0].goodsCostPrice,
        goodsStockNum: tableData.value[0].goodsStockNum,
        goodsStockWarn:tableData.value[0].goodsStockWarn,
        goodsWeight:tableData.value[0].goodsWeight,
        goodsVolume:tableData.value[0].goodsVolume,
        goodsSpecification:jsonStr,
        // goodsType:state.goodForm.goodsType,
        goodsVirtualSellCount:state.goodForm.goodsVirtualSellCount,
        goodsCategoryName:categoryName.value===''?state.defaultCate:categoryName.value,
        goodsNeedWriteOff:state.goodForm.goodsNeedWriteOff,
        // sequence:firstSequencex,
        // descriptions:allTableData.value,
        // specAndAttrs:arrayFormat,
        goodsMultiSpecification:state.goodForm.goodsspecs
      }
      if(state.goodForm.goodsspecs === '1'){
        params.sequence = firstSequencex.value
        params.descriptions = allTableData.value
        params.specAndAttrs = arrayFormat.value
        params.specificationName =  selectedSpecs.value
      }else{
        params.descriptions = allTableData.value
        params.specAndAttrs = arrayFormat.value
        params.specificationName =  headerLabel.value
      }
      console.log('params', params)
      if(option === 'edit'){
        if (id) {
        params.goodsId = id
        // 修改商品使用 put 方法
        httpOption = axios.put
      }
      }
    
      httpOption('/goods', params).then(() => {
        ElMessage.success(id ? '修改成功' : '添加成功')
        router.push({ path: '/good' })
      })
    }
  })
}
const handleBeforeUpload = (file) => {
  const sufix = file.name.split('.')[1] || ''
  if (!['jpg', 'jpeg', 'png'].includes(sufix)) {
    ElMessage.error('请上传 jpg、jpeg、png 格式的图片')
    return false
  }
}
const handleUrlSuccess = async (file) => {
  try {
    const previewUrl = await generatePreview(file.raw);
    state.goodForm.goodsCoverImg = previewUrl || '';
    
    const formData = new FormData();
    formData.append('file', file.raw);

    const res = await axios.post('/upload/file', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });

    if (res.url) {
      state.goodForm.goodsCoverImg = res.url;
      // If we're in single spec mode, update the tableData as well
      if (state.goodForm.goodsspecs === '0') {
        // tableData.value[0].goodsSkuImg = res.url;
      }
    }
  } catch (error) {
    console.error('生成预览失败:', error);
    ElMessage.error('文件读取失败');
  }
};

const handleUrlSuccessSkuForRow = async (file, row, index) => {
  try {
    const previewUrl = await generatePreview(file.raw);
    
    // Create form data for upload
    const formData = new FormData();
    formData.append('file', file.raw);

    // Upload the image
    const res = await axios.post('/upload/file', formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });

    // Update the specific row's image
    if (res.url) {
      // Update the row's image
      row.goodsSkuImg = res.url;
      
      // If this is the first row, also update the form's image
      if (index === 0) {
        state.goodForm.goodsSkuImg = res.url;
      }
      
      ElMessage.success('图片上传成功');
    }
  } catch (error) {
    console.error('图片上传失败:', error);
    ElMessage.error('图片上传失败');
  }
};

const handleChangeCate = (val) => {
  console.log(val, 'state.categoryId') 
  if(val!==undefined  ){
    if(val.length>1){
 state.categoryId = val[1] || 0
  }else{
    state.categoryId = val[0]|| 0
  }
  // state.categoryId = val[0]||val[1] || 0
  console.log(state.categoryId, 'state.categoryId') 
  }else{
    state.categoryId = ''
  }


// 获取选中节点的完整数据
const nodes = cascaderRef.value.getCheckedNodes();
  
  if (nodes.length > 0) {
    const currentData = nodes[0].data; // 当前节点数据
    const pathData = nodes[0].pathNodes; // 路径所有节点
    
    // // 示例获取属性
    console.log('当前节点ID:', currentData.value);
    console.log('分类名称:', currentData.label);
    console.log('所属层级:', currentData.categoryType);
    console.log('父级ID:', pathData[pathData.length-2]?.value);
    categoryName.value =  currentData.label
    categoryTypex.value = currentData.categoryType
    console.log('所属层级ss:',  categoryName.value);
  }

 // 手动触发表单验证
 goodRef.value?.validateField('categoryId')
}

// 监听所属类型变化
watch(selectedId, (newVal) => {
  if (newVal) {
    // 手动触发表单验证
    goodRef.value?.validateField('selectedId')
  }
})

// 监听所属商户变化
watch(selectedValuemerchants, (newVal) => {
  if (newVal) {
    // 手动触发表单验证
    goodRef.value?.validateField('goodsBelongMerchantId')
  }
})

const handleChange = (val) => {
  console.log('当前选中值:', val) // 输出 value 值（如 1001）
}

// 添加 watch 监听规格选择变化
watch(selectedSpecs, (newVal) => {
  if (state.goodForm.goodsspecs === '1') {
    // 手动触发表单验证
    goodRef.value?.validateField('selectedSpecs')
  }
})

// 添加新的规格值
const addSpecValue = (index) => {
  formItems.value[index].attrSpecs.push({ value: '' });
}

// 移除规格值
const removeSpecValue = (index, specIndex) => {
  if (formItems.value[index].attrSpecs.length > 1) {
    formItems.value[index].attrSpecs.splice(specIndex, 1);
  } else {
    ElMessage.warning('至少保留一个规格值');
  }
}

</script>

<style scoped>
/* 添加以下样式 */

.el-table {
  margin: 20px 0;
  margin-left: 30px;
  width: calc(100% - 30px);
  box-sizing: border-box;
}

.el-input-number,
.el-input {
  width: 100%;
}

.el-switch {
  margin-left: 8px;
}

span {
  cursor: pointer;
  user-select: none;
}

span:hover {
  color: #409eff;
}
  .add {
    /* display: flex; */
  }
  .add-container {
    flex: 1;
   margin-top: -30px;
  overflow-y: auto;
  padding: 16px;
  }
 
  .avatar-uploader {
    width: 100px;
    height: 100px;
    color: #ddd;
    font-size: 30px;
  }
  .avatar-uploader-icon {
    display: block;
    width: 100%;
    height: 100%;
    border: 1px solid #e9e9e9;
    padding: 32px 17px;
  }
  .preview-container {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
  margin-top: 20px;
}

.preview-item {
  position: relative;
  width: 100px;
  height: 100px;
  padding: 10px;
}

.preview-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 4px;
  
}

.delete-icon {
  position: absolute;
  
  right: 8px;
  cursor: pointer;
  background: #ff4d4f;
  color: white;
  border-radius: 50%;
  padding: 3px;
}

.edit-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.form-item {
  margin-top: 15px;
  padding: 15px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
}
.edit-mode {
  display: flex;
  flex-direction: column;  /* Stack elements vertically */
  gap: 10px;
  width: 100%;
}

.spec-values-wrapper {
  display: flex;
  flex-wrap: wrap;  /* Allow items to wrap */
  gap: 10px;
  width: 100%;
}

.spec-value-item {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;  /* Add space between rows */
}

.view-mode {
  display: flex;
  margin-top: 10px;
  gap: 10px;
}
.action-buttons {
 
  display: flex;
  gap: 10px;
}

.specs-container {
  display: flex;
  flex-direction: column; /* 使内部元素垂直排列 */
  gap: 20px;  /* 规格块之间的间距 */
  width: 100%; /* 确保容器宽度占满 */
}

.spec-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  background-color: #fafafa;
  width: 100%; /* 使每个规格块宽度占满容器，从而换行 */
  box-sizing: border-box; /* 包含内边距和边框在宽度内 */
}

.spec-form-item {
  margin-bottom: 0 !important;
}

.spec-form-item :deep(.el-form-item__label) {
  display: block !important;
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 15px;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.spec-form-item :deep(.el-form-item__content) {
  margin-left: 0 !important;
}

.input-group {
  display: flex; /* 将容器设置为 Flex 布局 */
  align-items: center; /* 垂直居中对齐子元素 */
  margin-bottom: 10px; /* 规格值之间的间距 */
  gap: 10px; /* 子元素之间的间距 */
}

/* 确保按钮样式正确 */
.el-button {
  /* margin-top: 10px; 这行可以移除，由 flex gap 控制间距 */
  margin-left: 0 !important; /* 移除 Element Plus 默认的 margin-left */
}

/* 调整添加按钮样式 */
.spec-item > .el-button { /* 精确选择每个规格块底部的添加按钮 */
    margin-top: 10px;
    margin-left: 0;
}


/* 调整表单项整体样式 */
.edit-item {
  margin-bottom: 0;
  /* display: flex; 这行可以移除，由 specs-container 控制 */
  /* align-items: center; 这行可以移除，由 input-group 控制对齐 */
  /* gap: 10px; 这行可以移除 */
}

/* 调整 el-table 的样式 */
.el-table {
  margin: 20px 0; /* 保持原有的 margin-top 和 bottom */
  /* 调整 margin-left 和 width */
  margin-left: 14px; /* 新的左边距：16px (父容器左padding) - 2px (可能的微调) */
  width: calc(100% - 14px - 16px); /* 宽度为父容器 content-box 宽度 - 表格左边距 - 父容器右padding */
  box-sizing: border-box; /* 确保 padding 和 border 不会增加总宽度 */
}

/* 确保 .add-container 有 padding */
.add-container {
    flex: 1;
    margin-top: -30px;
    overflow-y: auto;
    padding: 16px; /* 确认这里有 padding */
    /* 可能需要调整 padding-left 来配合表格的 margin-left */
    /* padding-left: 30px; 这是一种可能的调整，但会影响其他元素 */
}

/* 为了让表格的左边和表单项的左边对齐 (30px)，同时表格能填满父容器的宽度，
   我们让表格的左外边距为 14px (父容器左内边距 16px - 2px 微调)，
   然后宽度计算为 100% - 表格左外边距 - 父容器右内边距。
   这样表格的左边缘会在父容器左边缘 + 16px + 14px = 父容器左边缘 + 30px 的位置。
   表格的右边缘会在父容器左边缘 + 16px + (100% - 14px - 16px) = 父容器左边缘 + 16px + 100% - 30px
   如果 100% 是父容器的 content-box 宽度，那么表格右边缘会在 父容器左边缘 + 16px + (父容器 content-box 宽度) - 30px
   表格的左边距离父容器 content-box 左边是 30px。
   表格的右边距离父容器 content-box 右边是 (父容器 content-box 宽度) - (30px + calc(100% - 30px - 16px))
   = (父容器 content-box 宽度) - (30px + 父容器 content-box 宽度 - 30px - 16px)
   = (父容器 content-box 宽度) - (父容器 content-box 宽度 - 16px) = 16px
   这样表格右边距离父容器 content-box 右边是 16px，这正是父容器的右 padding 宽度。
   所以表格的右边缘会和父容器的右内边距边缘对齐，不会超出父容器的边框。
*/

.button-group {
  display: flex;
  gap: 10px;
  margin-top: 10px;
}

</style>
<style>
 .custom-menu .el-cascader-menu {
  max-height: 80px;
}

.el-tabs {
  border: none !important; /* 清除外层容器边框:ml-citation{ref="3" data="citationList"} */
}

.el-tabs__content {
  border: none !important; /* 去除内容区域边框:ml-citation{ref="3" data="citationList"} */
}

/* 固定定位的 Tab 栏 */
.fixed-tabs {
  /* position: sticky;
  top: 0;
  left: 0; */
  width: 100%;
  z-index: 1000;
  background: #fff;
  box-shadow: 0 2px 12px rgba(0,0,0,0.1); /* 增加层次感:ml-citation{ref="1" data="citationList"} */
}
</style>
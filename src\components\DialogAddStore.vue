<template>
  <el-dialog
    :title="type == 'add' ? '添加店铺' : (type == 'edit' ? '修改店铺' : '预览店铺')"
    v-model="state.visible"
    width="600px"
  >
    <el-form :model="state.ruleForm" :rules="state.rules" ref="formRef" label-width="100px" class="good-form">
      
      <el-form-item required label="店铺名称" prop="shopName">
        <el-input type="text" v-model="state.ruleForm.shopName" clearable></el-input>
      </el-form-item>
      <el-form-item required label="店铺手机号" prop="shopPhoneNo">
  <el-input 
    type="text" 
    v-model="state.ruleForm.shopPhoneNo" 
    placeholder="请输入手机号"
    clearable
    oninput="value=value.replace(/[^\d]/g,'')"
  ></el-input>
</el-form-item>
      <el-text 
  :truncated="true" 
  :line-clamp="2" 
  type="primary" 
  size="medium"
  style="margin-left: 30px;"
>
商户手机号作为商户登录账号


</el-text>
      <el-form-item required label="店铺logo" prop="shopLogo">
        <el-upload
          class="avatar-uploader"
          :action="state.shopLogo"
          accept="image/*"
          :headers="{
            token: state.token
          }"
          :auto-upload="false"
          :show-file-list="false"
          :before-upload="handleBeforeUpload"
          :on-change="handleUrlSuccess"
        >
          <img style="width: 200px; height: 100px; border: 1px solid #e9e9e9;" v-if="state.ruleForm.shopLogo" :src="state.ruleForm.shopLogo" class="avatar">
          <el-icon v-else class="avatar-uploader-icon"><Plus /></el-icon>
        </el-upload>
      </el-form-item>

      <el-form-item  required label="所属商户" prop="shopBelongMerchantId" >
        <el-select clearable filterable 
            v-model="state.ruleForm.shopBelongMerchantId"
            reserve-keyword  
            placeholder="请选择商户" 
            style="width: 300px; margin-right: 10px"
            @change="handleMerchantChange">
            <el-option
              v-for="item in optionsmerchants"
              :key="item.merchantId"
              :label="item.merchantName"
              :value="item.merchantId"
            />
          </el-select>
      </el-form-item>

    </el-form>
    <template v-if="type == 'preview' ? false:  true" #footer>
      <span class="dialog-footer">
        <el-button @click="state.visible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, ref ,onMounted} from 'vue'
import axios from '@/utils/axios'
import { localGet, uploadImgServer } from '@/utils'
import { ElMessage } from 'element-plus'

const props = defineProps({
  type: String,
  reload: Function
})
const optionsmerchants = ref([]);
const selectedValuemerchants = ref('');
const formRef = ref(null)
const state = reactive({
  uploadImgServer,
  token: localGet('token') || '',
  visible: false,
  ruleForm: {
    shopName: '',
    shopPhoneNo: '',
    shopLogo: '',
    shopBelongMerchantId: '',
    shopBelongMerchantName:''
  },
  rules: {
    shopLogo: [
      { required: 'true', message: '商户logo不能为空', trigger: ['change'] }
    ],
    shopName: [
      { required: 'true', message: '商户名称不能为空', trigger: ['change'] }
    ],
    shopPhoneNo: [
      { required: 'true', message: '商户手机号不能为空', trigger: ['change'] }
    ],
    shopBelongMerchantId: [
      { required: 'true', message: '所属商户不能为空', trigger: ['change'] }
    ], shopPhoneNo: [
    { 
      required: true, 
      message: '请输入手机号', 
      trigger: 'blur' 
    },
    { 
      type: 'number', 
      message: '手机号必须为数字', 
      trigger: 'blur',
      transform: (value) => Number(value) 
    },
    { 
      validator: (rule, value, callback) => {
        if (!/^1[3-9]\d{9}$/.test(value)) {
          callback(new Error('请输入正确的手机号'));
        } else {
          callback();
        }
      },
      trigger: 'blur'
    }
  ],
  },
  id: '',
  logourl:''
})

onMounted(() => {
  // getOrderList()
  // getMerchants()
})

const getOrderList = () => {
  // state.loading = true

  // console.log(selectedstateId ,'index')
  axios.get('/shops', {
    params: {
      pageNumber: 1,
      pageSize: 100,
      shopName: '',
      status: ''
    }
  }).then(res => {
    // optionsmerchants.value = res.list

  })
}

const getMerchants = () => {
  // state.loading = true

  // console.log(selectedstateId ,'index')
  axios.get('/merchants', {
    params: {
      pageNumber: 1,
      pageSize: 100,
      shopName: '',
      status: ''
    }
  }).then(res => {
    optionsmerchants.value = res.list

  })
}

// 获取详情
const getDetail = (id) => {
  axios.get(`/shops/${id}`).then(res => {
    state.ruleForm = {
      shopName: res.shopName,
      shopPhoneNo: res.shopPhoneNo,
      shopLogo: res.shopLogo,
      shopBelongMerchantId: res.shopBelongMerchantId
    }
  })
}
const handleBeforeUpload = (file) => {
  const sufix = file.name.split('.')[1] || ''
  if (!['jpg', 'jpeg', 'png'].includes(sufix)) {
    ElMessage.error('请上传 jpg、jpeg、png 格式的图片')
    return false
  }
}
// 上传图片
const handleUrlSuccess = async (file) => {
  try {
    const previewUrl = await generatePreview(file.raw);
    state.ruleForm.shopLogo = previewUrl || ''
    // console.log(state.ruleForm.merchantLogo,'previewUrl')
    // previewList.value.push(previewUrl);
    // form.images.push(file.raw);
    const formData = new FormData();
    formData.append('file', file.raw); // 文件字段
  // form.images.forEach((file, index) => {
  //   formData.append(`images[${index}]`, file);
  // });
  

  axios.post('/upload/file', formData, {
    headers: { 'Content-Type': 'multipart/form-data' }
  }).then(res => {
    // 处理成功逻辑
    // console.log(res, 'res');
    // if(res.resultCode === 200){
      state.ruleForm.shopLogo = res.url
    // }
  });
  } catch (error) {
    console.error('生成预览失败:', error);
    ElMessage.error('文件读取失败');
  }
}
// 生成Base64预览
const generatePreview = (file) => {
  return new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = (e) => resolve(e.target.result);
    reader.readAsDataURL(file);
  });
};
// 开启弹窗
const open = (id) => {
  state.visible = true
  console.log(id,'id')
  if (id) {
    state.id = id
    getDetail(id)
  } else {
    state.ruleForm = {
      shopName: '',
      shopPhoneNo: '',
      shopLogo: '',
      shopBelongMerchantId: '',
      shopBelongMerchantName:''
    }
  }

  getMerchants()

}
// 关闭弹窗
const close = () => {
  state.visible = false
}
const submitForm = () => {
  console.log(formRef.value.validate)
  formRef.value.validate((valid) => {
    if (valid) {
      if (props.type == 'add') {
        axios.post('/shops', {
          shopName: state.ruleForm.shopName,
          shopPhoneNo: state.ruleForm.shopPhoneNo,
          shopLogo: state.ruleForm.shopLogo,
          shopBelongMerchantId: state.ruleForm.shopBelongMerchantId,
          shopBelongMerchantName:state.ruleForm.shopBelongMerchantName
        }).then(() => {
          ElMessage.success('添加成功')
          state.visible = false
          if (props.reload) props.reload()
        })
      } else {
        axios.put('/shops', {
          shopId: state.id,
          shopName: state.ruleForm.shopName,
          shopPhoneNo: state.ruleForm.shopPhoneNo,
          shopLogo: state.ruleForm.shopLogo,
          shopBelongMerchantId: state.ruleForm.shopBelongMerchantId,
          shopBelongMerchantName:state.ruleForm.shopBelongMerchantName
        }).then(() => {
          ElMessage.success('修改成功')
          state.visible = false
          if (props.reload) props.reload()
        })
      }
    }
  })
}
const handleMerchantChange = (value) => {
  console.log(value)
  const selectedMerchant = optionsmerchants.value.find(item => item.merchantId  === value)
  if (selectedMerchant) {
    state.ruleForm.shopBelongMerchantName = selectedMerchant.merchantName
  } else {
    state.ruleForm.shopBelongMerchantName = ''
  }
  console.log(state.ruleForm.shopBelongMerchantName)
}
defineExpose({ open, close })
</script>

<style scoped>
  .avatar-uploader {
    width: 100px;
    height: 100px;
    color: #ddd;
    font-size: 30px;
  }
  .avatar-uploader >>> .el-upload {
    width: 100%;
    text-align: center;
  }
  .avatar-uploader-icon {
    display: block;
    width: 100%;
    height: 100%;
    border: 1px solid #e9e9e9;
    padding: 32px 17px;
  }
</style>
<template>
  <div style="width: 100%; max-height: 100%">
    <!--  -->
    <el-form ref="formRef" :model="form" :rules="rules" label-width="auto">
      <el-form-item label="服务包名称" prop="goodsName">
        <el-input v-model="form.goodsName" placeholder="请输入" />
      </el-form-item>
      <el-form-item label="选择商户" prop="goodsBelongMerchant">
        <el-select
          v-model="form.goodsBelongMerchant"
          placeholder="请选择"
          value-key="merchantId"
        >
          <el-option
            v-for="item in MerchantList"
            :key="item.merchantId"
            :label="item.merchantName"
            :value="item"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="选择供应商" prop="goodsBelongSupplier">
        <el-select
          v-model="form.goodsBelongSupplier"
          placeholder="请选择"
          value-key="supplierId"
        >
          <el-option
            v-for="item in supplierList"
            :key="item.supplierId"
            :label="item.supplierName"
            :value="item"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="选择分类" prop="goodsCategoryId">
        <el-cascader
          ref="goodsCategory"
          style="width: 100%"
          @change="testdd"
          @expand-change="eeeee"
          v-model="form.goodsCategoryId"
          :options="state.categoryList"
          placeholder="请选择"
          filterable
          clearable
        />
      </el-form-item>
      <el-divider />
      <div
        style="
          border: 1px solid #dcdcdc;
          border-radius: 12px;
          padding: 10px;
          margin-bottom: 10px;
        "
        v-for="(it, index) in form.services"
        :key="index"
      >
        <el-form-item
          label="选择服务类型"
          :prop="'services.' + index + '.serviceType'"
          :rules="[
            {
              required: true,
              message: '请选择服务类型',
              trigger: 'change',
            },
          ]"
        >
          <el-select v-model="it.serviceType" placeholder="请选择">
            <el-option
              v-for="item in spfwlx"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <div v-if="it.serviceType == '1'">
          <el-form-item
            label="选择商品"
            :prop="'services.' + index + '.goodsId'"
            :rules="[
              {
                required: true,
                message: '请选择商品',
                trigger: 'change',
              },
            ]"
          >
            <el-select
              v-model="it.goodsId"
              placeholder="请选择"
              @change="getSkuList(index, it, 'services')"
            >
              <el-option
                v-for="item in state.goodsList"
                :key="item.goodsId"
                :label="item.goodsName"
                :value="item.goodsId"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="选择sku"
            :prop="'services.' + index + '.serviceSku'"
            :rules="[
              {
                required: true,
                message: '请选择规格',
                trigger: 'change',
              },
            ]"
          >
            <el-select v-model="it.serviceSku" placeholder="请选择">
              <el-option
                v-for="item in it.skuList"
                :key="item.id"
                :label="item.description"
                :value="item.description"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            label="使用次数"
            :prop="'services.' + index + '.serviceUsageCount'"
            :rules="[
              {
                required: true,
                message: '请输入使用次数',
                trigger: 'change',
              },
              { type: 'number', message: '请填入数字' },
            ]"
          >
            <el-input v-model.number="it.serviceUsageCount" placeholder="请输入" />
          </el-form-item>
        </div>

        <div v-if="it.serviceType == '2'">
          <el-form-item label="服务名称" prop="goodsName">
            <el-input v-model="form.goodsName" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="APPID" prop="goodsName">
            <el-input v-model="form.goodsName" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="跳转地址" prop="goodsName">
            <el-input v-model="form.goodsName" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="服务次数" prop="goodsName">
            <el-input v-model="form.goodsName" placeholder="请输入" />
          </el-form-item>
        </div>

        <!--   在线咨询 复诊 电话咨询  -->
        <div
          v-if="it.serviceType == '3' || it.serviceType == '4' || it.serviceType == '5'"
        >
          <el-form-item label="服务次数" prop="goodsName">
            <el-input v-model="form.goodsName" placeholder="请输入" />
          </el-form-item>
        </div>

        <el-button @click="delservice(index, 'services')">删除服务</el-button>
      </div>

      <el-button @click="addservice('services')" type="primary" :icon="Plus"
        >增加服务</el-button
      >

      <el-divider />
      <!-- {{ form }} -->

      <el-form-item label="赠送服务" prop="serviceIsGift">
        <el-radio-group v-model="form.serviceIsGift">
          <el-radio :value="false">否</el-radio>
          <el-radio :value="true">是</el-radio>
        </el-radio-group>
      </el-form-item>
      <div
        style="
          border: 1px solid #dcdcdc;
          border-radius: 12px;
          padding: 10px;
          margin-bottom: 10px;
        "
        v-for="(it, index) in form.giftServices"
        :key="index"
      >
        <h1>赠送服务</h1>
        <el-form-item
          label="选择服务类型"
          :prop="'giftServices.' + index + '.serviceType'"
          :rules="[
            {
              required: true,
              message: '请选择服务类型',
              trigger: 'change',
            },
          ]"
        >
          <el-select v-model="it.serviceType" placeholder="请选择">
            <el-option
              v-for="item in spfwlx"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>

        <div v-if="it.serviceType == '1'">
          <el-form-item
            label="选择商品"
            :prop="'giftServices.' + index + '.goodsId'"
            :rules="[
              {
                required: true,
                message: '请选择商品',
                trigger: 'change',
              },
            ]"
          >
            <el-select
              v-model="it.goodsId"
              placeholder="请选择"
              @change="getSkuList(index, it, 'giftServices')"
            >
              <el-option
                v-for="item in state.goodsList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            label="选择sku"
            :prop="'giftServices.' + index + '.serviceSku'"
            :rules="[
              {
                required: true,
                message: '请选择规格',
                trigger: 'change',
              },
            ]"
          >
            <el-select v-model="it.serviceSku" placeholder="请选择">
              <el-option
                v-for="item in it.skuList"
                :key="item.id"
                :label="item.description"
                :value="item.serviceSku"
              />
            </el-select>
          </el-form-item>

          <el-form-item
            label="使用次数"
            :prop="'giftServices.' + index + '.serviceUsageCount'"
            :rules="[
              {
                required: true,
                message: '请输入使用次数',
                trigger: 'change',
              },
              { type: 'number', message: '请填入数字' },
            ]"
          >
            <el-input v-model.number="it.serviceUsageCount" placeholder="请输入" />
          </el-form-item>
        </div>

        <div v-if="it.serviceType == '2'">
          <el-form-item label="服务名称" prop="goodsName">
            <el-input v-model="form.goodsName" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="APPID" prop="goodsName">
            <el-input v-model="form.goodsName" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="跳转地址" prop="goodsName">
            <el-input v-model="form.goodsName" placeholder="请输入" />
          </el-form-item>
          <el-form-item label="服务次数" prop="goodsName">
            <el-input v-model="form.goodsName" placeholder="请输入" />
          </el-form-item>
        </div>

        <!--   在线咨询 复诊 电话咨询  -->
        <div
          v-if="it.serviceType == '3' || it.serviceType == '4' || it.serviceType == '5'"
        >
          <el-form-item label="服务次数" prop="goodsName">
            <el-input v-model="form.goodsName" placeholder="请输入" />
          </el-form-item>
        </div>

        <el-button @click="delservice(index, 'giftServices')">删除服务</el-button>
      </div>

      <el-button @click="addservice('giftServices')" type="primary" :icon="Plus"
        >增加赠送服务</el-button
      >

      <el-divider />

      <el-form-item label="服务包金额" prop="goodsSellingPrice">
        <el-input v-model.number="form.goodsSellingPrice" placeholder="" />
      </el-form-item>
      <el-form-item label="服务包销量" prop="goodsVirtualSellCount">
        <el-input
          v-model.number="form.goodsVirtualSellCount"
          placeholder="可设置虚拟销量，总销量-虚拟销量=实际销量"
        />
      </el-form-item>

      <el-form-item label="服务描述" prop="goodsDetailContent">
        <div ref="editor"></div>
      </el-form-item>

      <el-form-item label="选择机构" prop="unicode">
        <el-select
          v-model="form.unicode"
          placeholder="请选择机构"
          filterable
          clearable
          @change="getDepList"
        >
          <el-option
            v-for="item in state.hospList"
            :key="item.hospId"
            :label="item.hospName"
            :value="item.unicode"
          />
        </el-select>
      </el-form-item>

      <div>
        <h1>关联医生</h1>
        <el-form-item
          v-for="(it, index) in form.doctors"
          :label="'医生' + (index + 1)"
          :prop="'doctors.' + index + '.doctorId'"
          :rules="[
            {
              required: true,
              message: '请选择医生',
              trigger: 'change',
            },
          ]"
        >
          <div
            style="
              display: flex;
              justify-content: center;
              width: 100%;
              margin-bottom: 10px;
            "
          >
            <el-select
              v-model="it.deptId"
              placeholder="请选择科室"
              style="margin-right: 5px"
              @change="getDocs(index, it)"
            >
              <el-option
                v-for="item in state.depList"
                :key="item.deptId"
                :label="item.deptName"
                :value="item.deptId"
              />
            </el-select>
            <el-select
              v-model="it.doctorId"
              placeholder="请选择医生"
              style="margin-right: 5px"
              @change="changeDoc(it, index)"
            >
              <el-option
                v-for="item in it.docList"
                :key="item.doctorId"
                :label="item.doctorName"
                :value="item.doctorId"
                :disabled="isdisable(item)"
              />
            </el-select>
            <el-button type="primary" :icon="Minus" @click="minusDoc(index)" />
            <el-button type="primary" :icon="Plus" @click="addDoc(index)" />
          </div>
        </el-form-item>
        <el-button
          v-if="form.doctors.length == 0"
          type="primary"
          :icon="Plus"
          @click="addDoc()"
          >增加医生</el-button
        >
        <el-divider></el-divider>
      </div>

      <el-form-item label="使用期" prop="goodsValidPeriod">
        <el-input
          v-model.number="form.goodsValidPeriod"
          placeholder="请输入购买后起有效天数"
        />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" @click="submitForm(formRef, 5)">
          提交审核并上架
        </el-button>
        <el-button type="primary" @click="submitForm(formRef, 2)"> 保存草稿 </el-button>
        <el-button @click="resetForm(formRef)">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup>
/* public class ServiceDoctorVO implements Serializable {

    @ApiModelProperty("医院id")
    private String unicode;

    @ApiModelProperty("医生科室id")
    private String deptId;

    @ApiModelProperty("医生id")
    private String doctorId;

}
 */
/* public class ServicePackAddParam implements Serializable {

    @ApiModelProperty("服务包名称")
    @NotEmpty(message = "服务包名称不能为空")
    @Length(max = 128,message = "服务包名称内容过长")
    private String goodsName;

    @ApiModelProperty("服务包所属商户id")
    @NotNull(message = "服务包所属商户id不能为空")
    private Long goodsBelongMerchantId;

    @ApiModelProperty("服务包所属商户名称")
    @NotEmpty(message = "服务包所属商户名称不能为空")
    private String goodsBelongMerchantName;

    @ApiModelProperty("服务包所属供应商id")
    @NotNull(message = "服务包所属供应商id不能为空")
    private Long goodsBelongSupplierId;

    @ApiModelProperty("服务包所属供应商名称")
    @NotEmpty(message = "服务包所属供应商名称不能为空")
    private String goodsBelongSupplierName;

    @ApiModelProperty("关联分类id")
    private Long goodsCategoryId;

    @ApiModelProperty("关联分类名称")
    private String goodsCategoryName;

    @ApiModelProperty("服务包售价")
    @NotEmpty(message = "服务包售价不能为空")
    private BigDecimal goodsSellingPrice;

    @ApiModelProperty("服务包成本价")
    private BigDecimal goodsCostPrice;

    @ApiModelProperty("服务包上架状态")
    @NotNull(message = "服务包上架状态不能为空")
    private Integer goodsSellStatus;

    @ApiModelProperty("服务包结算方式")
    @NotEmpty(message = "服务包结算方式不能为空")
    private String goodsSettlementMethod;

    @ApiModelProperty("服务包结算比例")
    private String goodsSettlementPercentage;

    @ApiModelProperty("服务包虚拟销量")
    private Integer goodsVirtualSellCount;

    @ApiModelProperty("服务包详情")
    private String goodsDetailContent;

    private Integer goodsValidPeriod;

    @ApiModelProperty("服务包服务列表")
    @NotEmpty
    private List<ServicePackVO> servicePacks;

    @ApiModelProperty("服务包关联医生列表")
    private List<ServiceDoctorVO> doctors;

} */
/* public class ServicePackVO implements Serializable {

    @ApiModelProperty("服务类型")
    private Integer serviceType;

    @ApiModelProperty("服务名称")
    private String serviceName;

    @ApiModelProperty("商品sku")
    private String serviceSku;

    @ApiModelProperty("次数")
    private Integer serviceUsageCount;

    @ApiModelProperty("appid")
    private String serviceAppid;

    @ApiModelProperty("跳转地址")
    private String serviceRedirectUrl;

    @ApiModelProperty("服务包关联机构社会统一信用代码")
    private String unicode;

} */
import { onMounted, reactive, ref } from "vue";
import { ElMessage } from "element-plus";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import { HomeFilled, Delete } from "@element-plus/icons-vue";
import axios from "@/utils/axios";
import { localGet, uploadImgServer, uploadImgsServer } from "@/utils";
import { Minus, Plus, Search, Share, Upload } from "@element-plus/icons-vue";
import WangEditor from "wangeditor";
import { inject } from "vue";
const pMhandleOption = inject("pMhandleOption"); // 使用 inject 获取父组件的方法
defineProps(["MerchantList", "supplierList"]);

const testdd = (val1, val2, val3) => {
  debugger;
  console.log(1111);
  console.log(val1, val2, val3);
};

const eeeee = (val1, val2, val3) => {
  debugger;
  console.log(222);
  console.log(val1, val2, val3);
};
const goodsCategory = ref(null);
const editor = ref(null);
const formRef = ref(null);
const form = reactive({
  goodsName: "",
  goodsBelongMerchant: "",
  goodsBelongSupplier: "",
  goodsCategoryId: "",
  services: [{ serviceType: "" }],
  giftServices: [],
  doctors: [{ deptId: "", doctorId: "", staffTechnicalTitleName: "" }],
});
const rules = reactive({
  goodsName: [{ required: true, message: "请输入服务包名称", trigger: "blur" }],
  goodsBelongMerchant: [{ required: true, message: "请选择商户", trigger: "blur" }],
  goodsBelongSupplier: [{ required: true, message: "请选择供应商", trigger: "blur" }],
  serviceIsGift: [{ required: true, message: "请选择是否赠送服务", trigger: "change" }],
  goodsVirtualSellCount: [{ type: "number", message: "请填入数字" }],
  goodsSellingPrice: [
    { required: true, message: "请输入服务包金融", trigger: "change" },
    { type: "number", message: "请填入数字" },
  ],
  goodsValidPeriod: [{ type: "number", message: "请填入数字" }],
  /* name: [
    { required: true, message: "Please input Activity name", trigger: "blur" },
    { min: 3, max: 5, message: "Length should be 3 to 5", trigger: "blur" },
  ], */
});
const state = reactive({
  token: localGet("token") || "",
  goodsList: [],
  hospList: [],
  categoryList: [{ categoryType: 0, children: null, label: "ceshi", value: 130 }],
  /* form: {
    goodsName: "",
    goodsBelongMerchant: "",
    goodsBelongSupplier: "",
    goodsCategoryId: "",
    services: [{ serviceType: "" }],
    giftServices: [],
    doctors: [{ deptId: "", doctorId: "", staffTechnicalTitleName: "" }],
  }, */
});

const spfwlx = [
  { id: 1, name: "商品" },
  { id: 2, name: "第三方服务" },
  { id: 3, name: "在线咨询" },
  { id: 4, name: "电话咨询" },
  { id: 5, name: "在线复诊" },
];

const disableDoc = () => {
  let z = false;
  // 判断科室id-医生id是否已经被选中 doctors
  for (let index = 0; index < form.doctors.length; index++) {
    const element = form.doctors[index];
    if (element.deptId == item.deptId && element.doctorId == item.doctorId) {
      z = true;
      return z;
    }
  }
  return z;
};

const isdisable = (item) => {
  let z = false;
  // 判断科室id-医生id是否已经被选中 doctors
  for (let index = 0; index < form.doctors.length; index++) {
    const element = form.doctors[index];
    if (element.deptId == item.deptId && element.doctorId == item.doctorId) {
      z = true;
      return z;
    }
  }
  return z;
};

// 删除医生
const minusDoc = (index) => {
  form.doctors.splice(index, 1);
  console.log(form.doctors);
};
// 新增医生
const addDoc = (index) => {
  if (form.doctors.length == 0) {
    form.doctors.push({ deptId: "", doctorId: "", staffTechnicalTitleName: "" });
  } else {
    form.doctors.splice(index, 0, {
      deptId: "",
      doctorId: "",
      staffTechnicalTitleName: "",
    });
  }
};

// 删除服务
const delservice = (index, type) => {
  form[type].splice(index, 1);
  console.log(form[type]);
};
const addservice = (type) => {
  form[type].push({ serviceType: 1 });
};

const getCategoryList = () => {
  axios
    .get("/categories", {
      params: {
        pageNumber: 1,
        pageSize: 1000,
        categoryLevel: 1,
      },
    })
    .then((res) => {
      let list = res.list;

      const getList = (item) => {
        const haveChildren =
          Array.isArray(item.childrenList) && item.childrenList.length > 0;
        return {
          value: item.categoryId,
          label: item.categoryName,
          categoryType: item.categoryType,
          children: haveChildren
            ? item.childrenList.map((it) => {
                return getList(it);
              })
            : null,
        };
      };

      state.categoryList = list.map((item) => {
        debugger;
        return getList(item);
      });
      console.log(state.categoryList);

      //   return list_;
    });
};
/* const category = {
  lazy: false,
  lazyLoad(node, resolve) {
    const { level = 0, value } = node;
    if (level === 0) {
      axios
        .get("/categories", {
          params: {
            pageNumber: 1,
            pageSize: 1000,
          },
        })
        .then((res) => {
          const list = res.list;
          const nodes = list.map((item) => ({
            value: item.categoryId,
            label: item.categoryName,
            categoryType: item.categoryType,
            // leaf: level > 1
            leaf: item.childrenList === null,
          }));
          resolve(nodes);
        });
    } else if (level === 1) {
      axios
        .get("/categoryList", {
          params: {
            pageNumber: 1,
            pageSize: 1000,
            categoryLevel: level + 1,
            parentId: value || 0,
          },
        })
        .then((res) => {
          const list = res.list;
          const nodes = list.map((item) => ({
            value: item.categoryId,
            label: item.categoryName,
            categoryType: item.categoryType,
            // leaf: level > 1
            leaf: true,
          }));
          resolve(nodes);
        });
    }
  },
}; */
let instance;
const initEditor = () => {
  if (instance) {
    return;
  }
  instance = new WangEditor(editor.value);
  instance.config.showLinkImg = false;
  instance.config.showLinkImgAlt = false;
  instance.config.showLinkImgHref = false;
  instance.config.uploadImgMaxSize = 2 * 1024 * 1024; // 2M
  instance.config.uploadFileName = "file";
  instance.config.uploadImgHeaders = {
    token: state.token,
  };

  // 修改图片上传配置
  instance.config.uploadImgServer = uploadImgsServer; // 使用与 el-upload 相同的上传地址

  // 自定义图片上传
  instance.config.uploadImgHooks = {
    // 上传图片之前
    before: function (file) {
      // 添加文件存在性检查
      // if (!file || !file.name) {
      //   ElMessage.error('无效的文件1')
      //   return false
      // }
      // 检查文件类型
      // const sufix = file.name.split('.')[1]?.toLowerCase() || ''
      // if (!['jpg', 'jpeg', 'png'].includes(sufix)) {
      //   ElMessage.error('请上传 jpg、jpeg、png 格式的图片')
      //   return false
      // }
      // // 检查文件大小
      // const maxSize = 2 * 1024 * 1024 // 2MB
      // if (file.size > maxSize) {
      //   ElMessage.error('图片大小不能超过 2MB')
      //   return false
      // }
      // return true
    },

    // 上传图片进度条
    customUpload: function (file, insertFn) {
      if (!file) {
        ElMessage.error("无效的文件");
        return;
      }

      // 创建 FormData
      const formData = new FormData();
      formData.append("file", file.raw);

      // 使用 axios 上传
      axios
        .post("/upload/file", formData, {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        })
        .then((res) => {
          console.log("上传响应:", res); // 添加日志查看返回结果

          // 直接使用返回的 url
          if (res && res.url) {
            // 直接调用 insertFn 插入图片
            insertFn(res.url);
            ElMessage.success("图片上传成功");
          } else {
            ElMessage.error("图片上传失败：未获取到图片地址");
          }
        })
        .catch((err) => {
          console.error("图片上传失败:", err);
          ElMessage.error("图片上传失败：" + (err.message || "未知错误"));
        });
    },

    // 上传图片失败
    fail: function (xhr, editor, resData) {
      ElMessage.error("图片上传失败");
    },

    // 上传图片错误
    error: function (xhr, editor, resData) {
      ElMessage.error("图片上传出错");
    },

    // 上传图片超时
    timeout: function (xhr, editor, resData) {
      ElMessage.error("图片上传超时");
    },

    // 添加 customInsert 钩子
    customInsert: function (insertImgFn, result) {
      // result 即服务端返回的接口
      // console.log('上传响应:', result)
      if (result) {
        insertImgFn(result.data.url);
      }
    },
  };

  // 其他配置保持不变
  Object.assign(instance.config, {
    onchange() {
      console.log("change");
    },
  });

  instance.create();
};
const initFwb = (fwbId) => {
  debugger;
  console.log("eeee111111111111");
  initEditor();
  initGoods();
  initHospList();
  getCategoryList();
  if (fwbId) {
    // 有服务包id  为编辑  获取原始数据
    initData(fwbId);
  }

  //
};

const initData = (fwbId) => {
  axios
    .get(`/servicePacks/${fwbId}`, {
      params: {},
    })
    .then((res) => {
      debugger;
      //   form = { ...res };
      form.goodsName = res.goodsName;

      form.goodsBelongMerchant = {
        merchantId: res.goodsBelongMerchantId,
        merchantName: res.goodsBelongMerchantName,
      };
      form.goodsBelongSupplier = {
        supplierId: res.goodsBelongSupplierId,
        supplierName: res.goodsBelongSupplierName,
      };
      form.goodsCategoryId = [15, res.goodsCategoryId];
      form.services = res.services;

      //   模拟数据
      //   form.services = [
      //     {
      //       goodsId: 11031,
      //       serviceAppid: null,
      //       serviceIsGift: false,
      //       serviceName: null,
      //       serviceRedirectUrl: null,
      //       serviceSku: "个",
      //       serviceType: 1,
      //       serviceUsageCount: 2,
      //     },
      //   ];
      form.giftServices = res.giftServices;
      form.services.forEach((it, index) => {
        getSkuList(index, it, "services", 1);
      });
      form.giftServices.forEach((it, index) => {
        getSkuList(index, it, "giftServices", 1);
      });
      form.doctors = res.doctors;

      // state.loading = false;
    });
};

const changeDoc = (it, index_) => {
  for (let index = 0; index < it.docList.length; index++) {
    const element = it.docList[index];
    if (it.doctorId == element.doctorId) {
      form.doctors[index_].staffTechnicalTitleName = element.staffTechnicalTitleName;
      return;
    }
  }
};
const getDocs = (index, it) => {
  form.doctors[index].doctorId = "";
  state.loading = true;
  axios
    .get("/servicePack/doctor/list", {
      params: {
        unicode: form.unicode,
        deptId: it.deptId,
        pageNumber: 1,
        pageSize: 999999,
      },
    })
    .then((res) => {
      form.doctors[index].docList = res.list;
      // state.loading = false;
    });
};
const getDepList = () => {
  form.doctors = [];
  state.loading = true;
  axios
    .get("/servicePack/department/list", {
      params: {
        unicode: form.unicode,
        pageNumber: 1,
        pageSize: 999999,
      },
    })
    .then((res) => {
      state.depList = res.list;
      // state.loading = false;
    });
};

// get /servicePack/doctor/list 医生列表，入参unicode, deptId

// /servicePack/hospital/list
const initHospList = () => {
  // 获取医院
  state.loading = true;
  axios
    .get("/servicePack/hospital/list", {
      params: {
        pageNumber: 1,
        pageSize: 999999,
      },
    })
    .then((res) => {
      state.hospList = res.list;
      // state.loading = false;
    });
};

const initGoods = () => {
  // 获取商品数据

  state.loading = true;
  axios
    .get("/goods/list", {
      params: {
        pageNumber: 1,
        pageSize: 999999,
      },
    })
    .then((res) => {
      state.goodsList = res.list;
      // state.loading = false;
    });
};

const submitForm = (formRef_, status) => {
  debugger;
  console.log("22222", formRef.value.fields);
  console.log(form);

  formRef.value.validate((valid, fields) => {
    if (valid) {
      console.log("submit!");

      submitData(status);
      // 描述
      //   instance.txt.html()
    } else {
      console.log("error submit!", fields);
    }
  });
};

const submitData = (status) => {
  let data = { ...form };
  //   data.goodsCategoryId = form.goodsCategoryId.toString();
  //   data.goodsCategoryId = data.goodsCategoryId ? parseInt(data.goodsCategoryId) : "";
  if (form.goodsCategoryId && form.goodsCategoryId.length > 0) {
    data.goodsCategoryId = form.goodsCategoryId[form.goodsCategoryId.length - 1];
    // console.log(
    //   "goodsCategoryIdgoodsCategoryIdgoodsCategoryId",
    //   goodsCategory.value.getCheckedNodes()[0].label
    // );
    data.goodsCategoryName = goodsCategory.value.getCheckedNodes()[0].label;
  }
  data.goodsDetailContent = instance.txt.html();
  data.goodsSellStatus = status;
  data.goodsBelongMerchantId = data.goodsBelongMerchant.merchantId;
  data.goodsBelongMerchantName = data.goodsBelongMerchant.merchantName;
  data.goodsBelongSupplierId = data.goodsBelongSupplier.supplierId;
  data.goodsBelongSupplierName = data.goodsBelongSupplier.supplierName;

  delete data.goodsBelongMerchant;
  delete data.goodsBelongSupplier;
  data.services.forEach((service) => {
    delete service.skuList;
  });
  data.doctors.forEach((doctor) => {
    delete doctor.docList;
  });

  axios.post("/servicePacks", data).then((res) => {
    console.log("保存成功", res); // 添加日志查看返回结果
    ElMessage.success("保存成功");
    pMhandleOption();
    // // 直接使用返回的 url
    // if (res && res.url) {
    //   // 直接调用 insertFn 插入图片
    //   insertFn(res.url);
    //   ElMessage.success("图片上传成功");
    // } else {
    //   ElMessage.error("图片上传失败：未获取到图片地址");
    // }
  });
};

// get /multiSpecification/selectByGoodsId 根据goodsId找规格
const getSkuList = (index, it, type, needQK) => {
  if (!needQK) form[type][index].serviceSku = "";
  // 获取商品数据

  state.loading = true;
  axios
    .get("/multiSpecification/selectByGoodsId", {
      params: {
        goodsId: it.goodsId,
        pageNumber: 1,
        pageSize: 999999,
      },
    })
    .then((res) => {
      form[type][index].skuList = res;
      // state.loading = false;
    })
    .catch((err) => {
      console.log(err);
    });
};

defineExpose({ initFwb });
const showDetail = (row) => {
  // todo
  console.log("eeee", row);

  // /orders/{orderId}
  state.loading = true;
  axios.get("/orders/" + row.orderId, null).then((res) => {
    console.log(res);
    state.dialogVisible = true;
    state.detail = res;

    state.loading = false;
  });
};

// 获取列表方法

// 获取商户列表方法
const getSysUsersList = () => {
  state.loading = true;
  axios
    .get("/merchants", {
      params: {
        pageNumber: 1,
        pageSize: 999999,
      },
    })
    .then((res) => {
      state.userList = res.list;
      // state.loading = false;
    });
};
// 获取供应商列表
const getSupplierList = () => {
  state.loading = true;
  axios
    .get("/supplier/list", {
      params: {
        pageNumber: 1,
        pageSize: 999999,
      },
    })
    .then((res) => {
      //   state.userList = res.list;
      // state.loading = false;
    });
};

// 触发过滤项方法
const handleOption = () => {
  state.currentPage = 1;
  getOrderList();
};
const clearSearch = () => {
  state.currentPage = 1;
  state.currentPage = 1;
  state.orderNo = "";
  state.createTime = "";
  state.organId = "";
  state.phoneNo = "";
  state.refundSeq = "";
  state.belong = "";
  getOrderList();
};

// checkbox 选择项
const handleSelectionChange = (val) => {
  state.multipleSelection = val;
};
// 翻页方法
const changePage = (val) => {
  state.currentPage = val;
  getOrderList();
};
// 配货方法
const handleConfig = (id) => {
  let params;
  // 当个配置
  if (id) {
    params = [id];
  } else {
    if (!state.multipleSelection.length) {
      console.log("state.multipleSelection", state.multipleSelection.length);
      ElMessage.error("请选择项");
      return;
    }
    // 多选配置
    params = state.multipleSelection.map((i) => i.orderId);
  }
  axios
    .put("/orders/checkDone", {
      ids: params,
    })
    .then(() => {
      ElMessage.success("配货成功");
      getOrderList();
    });
};
// 出库方法
const handleSend = (id) => {
  let params;
  if (id) {
    params = [id];
  } else {
    if (!state.multipleSelection.length) {
      ElMessage.error("请选择项");
      return;
    }
    params = state.multipleSelection.map((i) => i.orderId);
  }
  axios
    .put("/orders/checkOut", {
      ids: params,
    })
    .then(() => {
      ElMessage.success("出库成功");
      getOrderList();
    });
};
// 关闭订单方法
const handleClose = (id) => {
  let params;
  if (id) {
    params = [id];
  } else {
    if (!state.multipleSelection.length) {
      ElMessage.error("请选择项");
      return;
    }
    params = state.multipleSelection.map((i) => i.orderId);
  }
  axios
    .put("/orders/close", {
      ids: params,
    })
    .then(() => {
      ElMessage.success("关闭成功");
      getOrderList();
    });
};
</script>

<template>
  <el-dialog
    v-model="state.visible"
    width="600px"
    :show-close="false"
    :show-header="false"
    center
  >
    <div class="dialog-title">添加供应商</div>
    
    <el-form 
      :model="state.ruleForm" 
      :rules="state.rules" 
      ref="formRef" 
      label-width="120px" 
      class="supplier-form"
    >
      <el-form-item label="供应商名称" prop="supplierName">
        <el-input 
          v-model="state.ruleForm.supplierName" 
          placeholder="请输入供应商名称"
        />
      </el-form-item>
      
      <el-form-item label="供应商手机号" prop="supplierPhone">
        <el-input 
          v-model="state.ruleForm.supplierPhone" 
          placeholder="请输入供应商手机号"
        />
        <div class="form-tip">供应商手机号作为供应商登录账号</div>
      </el-form-item>
      
      <el-form-item label="供应商户" prop="supplierMerchants">
        <div class="merchant-list">
          <div 
            v-for="(merchant, index) in state.ruleForm.supplierMerchants" 
            :key="index"
            class="merchant-item"
          >
            <el-select
              v-model="state.ruleForm.supplierMerchants[index]"
              placeholder="请选择供应商户"
              style="flex: 1;"
            >
              <el-option
                v-for="option in state.merchantOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
            <el-button 
              v-if="state.ruleForm.supplierMerchants.length > 1"
              @click="removeMerchant(index)"
              class="remove-btn"
            >
              -
            </el-button>
            <el-button 
              v-if="index === state.ruleForm.supplierMerchants.length - 1"
              @click="addMerchant"
              class="add-btn"
            >
              +
            </el-button>
          </div>
        </div>
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-select v-model="state.ruleForm.status" placeholder="请选择状态">
          <el-option label="上架" :value="1" />
          <el-option label="下架" :value="0" />
        </el-select>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import axios from '@/utils/axios'

const props = defineProps({
  type: String,
  reload: Function
})

const formRef = ref(null)
const state = reactive({
  visible: false,
  ruleForm: {
    supplierName: '',
    supplierPhone: '',
    supplierMerchants: [''],
    status: 1
  },
  rules: {
    supplierName: [
      { required: true, message: '供应商名称不能为空', trigger: 'blur' }
    ],
    supplierPhone: [
      { required: true, message: '供应商手机号不能为空', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
    ],
    supplierMerchants: [
      { required: true, message: '供应商户不能为空', trigger: 'change' }
    ],
    status: [
      { required: true, message: '状态不能为空', trigger: 'change' }
    ]
  },
  merchantOptions: [
  ]
})

// 添加商户选项
const addMerchant = () => {
  state.ruleForm.supplierMerchants.push('')
}

// 移除商户选项
const removeMerchant = (index) => {
  if (state.ruleForm.supplierMerchants.length > 1) {
    state.ruleForm.supplierMerchants.splice(index, 1)
  }
}

// 打开对话框
const open = () => {
  state.visible = true
  // 重置表单
  state.ruleForm = {
    supplierName: '',
    supplierPhone: '',
    supplierMerchants: [''],
    status: 1
  }
  //获取merchants
  axios.get('/merchants?pageNumber=1&pageSize=100&status=1')
    .then(response => {
      const merchants = response.list
      state.merchantOptions = merchants.map(merchant => ({
        label: merchant.merchantName,
        value: merchant.merchantName
      }))
    })
    .catch(error => {
      console.error('获取商户列表失败:', error)
    })
  // 清除验证
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 关闭对话框
const close = () => {
  state.visible = false
}

// 取消
const handleCancel = () => {
  close()
}

// 确定
const handleConfirm = async () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      const validMerchants = state.ruleForm.supplierMerchants.filter(merchant => merchant.trim() !== '')
      
      if (validMerchants.length === 0) {
        ElMessage.error('请至少选择一个供应商户')
        return
      }
      
      const submitData = {
        supplierName: state.ruleForm.supplierName,
        supplierPhoneNo: state.ruleForm.supplierPhone,
        merchantLists: validMerchants,
        status: state.ruleForm.status.toString()
      }
      
      try {
        await axios.post('/suppliers', submitData)
        ElMessage.success('添加成功')
        close()
        if (props.reload) {
          props.reload()
        }
      } catch (error) {
        console.error('添加供应商失败:', error)
        ElMessage.error('添加供应商失败')
      }
    }
  })
}

defineExpose({ open, close })
</script>

<style scoped>
.dialog-title {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.supplier-form {
  padding: 0 20px;
}

.form-tip {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 5px;
}

.merchant-list {
  width: 100%;
}

.merchant-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.merchant-item:last-child {
  margin-bottom: 0;
}

.add-btn, .remove-btn {
  width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
}

.add-btn {
  background-color: #409eff;
  color: white;
  border: none;
}

.add-btn:hover {
  background-color: #66b1ff;
}

.remove-btn {
  background-color: #f56c6c;
  color: white;
  border: none;
}

.remove-btn:hover {
  background-color: #f78989;
}

.dialog-footer {
  text-align: center;
  padding: 20px 0 10px;
}

.dialog-footer .el-button {
  width: 100px;
  margin: 0 10px;
}
</style>

<template>
  <el-dialog
    v-model="visible"
    width="350px"
    :show-close="false"
    :show-header="false"
    center
    class="custom-confirm-dialog"
  >
    <div class="confirm-content">
      <slot>{{ message }}</slot>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel" class="cancel-btn">{{ cancelText }}</el-button>
        <el-button type="primary" @click="handleConfirm" class="confirm-btn">{{ confirmText }}</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref } from 'vue';

const props = defineProps({
  title: {
    type: String,
    default: '提示'
  },
  message: {
    type: String,
    default: '确认执行此操作?'
  },
  confirmText: {
    type: String,
    default: '是'
  },
  cancelText: {
    type: String,
    default: '否'
  }
});

const emit = defineEmits(['confirm', 'cancel']);

const visible = ref(false);

const handleConfirm = () => {
  visible.value = false;
  emit('confirm');
};

const handleCancel = () => {
  visible.value = false;
  emit('cancel');
};

const open = () => {
  visible.value = true;
};

defineExpose({ open });
</script>

<style scoped>
/* 标题栏已通过 show-header="false" 禁用 */

.custom-confirm-dialog {
  display: flex;
  align-items: center;
  justify-content: center;
}

.custom-confirm-dialog :deep(.el-dialog) {
  margin: 0 auto !important;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border-radius: 8px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.custom-confirm-dialog :deep(.el-dialog__body) {
  padding: 50px 20px 40px;
}

.confirm-content {
  text-align: center;
  font-size: 22px;
  font-weight: bold;
}

.dialog-footer {
  display: flex;
  justify-content: space-between;
  width: 100%;
  border-top: 1px solid #eee;
  padding-top: 10px;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  height: 50px;
  font-size: 18px;
  border: none;
  border-radius: 0;
  transition: all 0.3s;
}

.cancel-btn {
  color: #666;
  background-color: transparent;
}

.cancel-btn:hover {
  color: #333;
  background-color: #f5f7fa;
}

.confirm-btn {
  color: #409eff;
  background-color: transparent;
}

.confirm-btn:hover {
  color: #66b1ff;
  background-color: #ecf5ff;
}

.custom-confirm-dialog :deep(.el-dialog__footer) {
  padding: 0;
}

.custom-confirm-dialog :deep(.el-button:focus, .el-button:hover) {
  background-color: transparent;
  border-color: transparent;
}

.custom-confirm-dialog :deep(.el-button--primary:focus, .el-button--primary:hover) {
  color: #66b1ff;
}
</style>

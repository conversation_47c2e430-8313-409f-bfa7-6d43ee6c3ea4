<template>
  <el-dialog
    title="详情"
    v-model="state.visible"
    width="1000px"
    class="custom-dialog"
  >
    <!-- Tab Buttons -->
    <div class="tab-buttons">
      <el-button
        :type="activeTab === 'basic' ? 'primary' : 'info'"
        @click="activeTab = 'basic'"
        size="small"
      >
        基本信息
      </el-button>
      <el-button
        :type="activeTab === 'service' ? 'primary' : 'info'"
        @click="activeTab = 'service'"
        size="small"
      >
        服务信息
      </el-button>
    </div>

    <!-- Basic Info Section -->
    <div v-if="activeTab === 'basic'">
      <el-form :model="basicInfoData" label-width="100px" class="basic-info-form">
        <el-row>
          <el-col :span="12">
            <el-form-item label="服务包名称：">
              {{ basicInfoData.servicePackageName }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="选择机构：">
              {{ basicInfoData.selectedOrganization }}
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="8">
            <el-form-item label="服务包金额：">
              {{ basicInfoData.servicePackageAmount }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="有效期：">
              {{ basicInfoData.validityPeriod }}
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="状态：">
              {{ basicInfoData.status }}
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="关联医生：">
          <div class="doctor-tags">
            <el-tag v-for="(doctor, index) in basicInfoData.associatedDoctors" :key="index" type="success" size="small" style="margin-right: 5px; margin-bottom: 5px;">
              {{ doctor }}
            </el-tag>
          </div>
        </el-form-item>

        <el-form-item label="服务描述：">
          <el-image
            v-if="basicInfoData.serviceDescriptionImg"
            style="width: 200px; height: 150px"
            :src="basicInfoData.serviceDescriptionImg"
            fit="contain"
          ></el-image>
        </el-form-item>

        <el-form-item label="审核结果：">
           <el-select style="width: 300px;" v-model="basicInfoData.reviewResult" placeholder="请选择状态">
            
            <el-option label="审核通过" value="failed"></el-option>
            <el-option label="审核不通过" value="pending"></el-option>
            <!-- Add more options as needed -->
          </el-select>
        </el-form-item>

    <!-- Buttons moved here -->
    <el-form-item  class="centered-buttons-item">
           <span class="dialog-footer">
            <el-button @click="state.visible = false">取 消</el-button>
            <el-button type="primary" @click="submitForm">确 定</el-button>
          </span>
        </el-form-item>

      </el-form>
    </div>

    <!-- Service Info Section -->
    <div v-if="activeTab === 'service'">
      <div v-for="(item, index) in serviceInfoList" :key="index" class="service-item">
        <el-row>
          <el-col :span="12">
            <el-form-item label="服务类型：">
              {{ item.serviceType }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="选择商品：">
              {{ item.selectedProduct }}
            </el-form-item>
          </el-col>
        </el-row>

        <el-row>
          <el-col :span="12">
            <el-form-item label="选择SKU：">
              {{ item.selectedSku }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="使用次数：">
              {{ item.usageCount }}
            </el-form-item>
          </el-col>
        </el-row>
        <!-- Separator -->
        <el-divider v-if="index < serviceInfoList.length - 1"></el-divider>
      </div>
       <el-button type="success" size="small" style="margin-top: 10px;">赠送服务</el-button>
    </div>


    <!-- <template v-if="type == 'preview' ? false:  true" #footer>
      <span class="dialog-footer">
        <el-button @click="state.visible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </span>
    </template> -->
  </el-dialog>
</template>

<script setup>
import { reactive, ref ,onMounted, watch } from 'vue'
import axios from '@/utils/axios'
import { localGet, uploadImgServer } from '@/utils'
import { ElMessage } from 'element-plus'

const props = defineProps({
  type: String,
  reload: Function
})

// Reactive state for dialog visibility and form data
const state = reactive({
  uploadImgServer,
  token: localGet('token') || '',
  visible: false,
  // Keep the original ruleForm if needed for other parts,
  // but the display data will be handled by basicInfoData and serviceInfoList
  ruleForm: { /* ... original form fields ... */ },
  rules: { /* ... original form rules ... */ },
  id: '',
  logourl:'',
  status:''
})

// State for active tab
const activeTab = ref('basic'); // 'basic' or 'service'

// Reactive data for Basic Info tab
const basicInfoData = reactive({
  servicePackageName: '',
  selectedOrganization: '',
  servicePackageAmount: 0,
  validityPeriod: '',
  status: '',
  associatedDoctors: [],
  serviceDescriptionImg: '',
  reviewResult: '',
});

// Reactive data for Service Info tab (list of items)
const serviceInfoList = ref([]);

// --- API Fetching Functions (Placeholders) ---
// Replace these with your actual API calls

const fetchBasicInfo = async (id) => {
  try {
    // Replace with your actual API endpoint and data mapping
    // Example: const res = await axios.get(`/api/service-package-details/${id}`);
    // Assuming the API returns an object like:
    // {
    //   servicePackageName: '服务包名称',
    //   selectedOrganization: '选择机构',
    //   servicePackageAmount: 300.00,
    //   validityPeriod: '90天',
    //   status: '审核中',
    //   associatedDoctors: ['医生A', '医生B', '医生C'], // Array of strings
    //   serviceDescriptionImg: 'url/to/image.jpg',
    //   reviewResult: 'pending', // Or 'passed', 'failed'
    // }
    const dummyData = {
       servicePackageName: '体检服务包',
       selectedOrganization: '杭州市第三人民医院',
       servicePackageAmount: 500.00,
       validityPeriod: '180天',
       status: '已通过',
       associatedDoctors: ['张医生 - 内科', '李医生 - 骨科', '王医生 - 皮肤科'],
       serviceDescriptionImg: 'https://fuss10.elemecdn.com/e/5d/4a731a90594a4cd59a75a25da1b3ejpg.jpg', // Example image URL
       reviewResult: '',
    };
    Object.assign(basicInfoData, dummyData);
  } catch (error) {
    console.error('Error fetching basic info:', error);
    ElMessage.error('获取基本信息失败');
  }
};

const fetchServiceInfo = async (id) => {
  try {
    // Replace with your actual API endpoint and data mapping
    // Example: const res = await axios.get(`/api/service-package-services/${id}`);
    // Assuming the API returns an array like:
    // [
    //   {
    //     serviceType: '商品',
    //     selectedProduct: '商品名称',
    //     selectedSku: '短款 XL 白色',
    //     usageCount: 2,
    //   },
    //   {
    //     serviceType: '第三方服务',
    //     selectedProduct: '服务名称', // Or serviceName
    //     selectedSku: '', // Not applicable for this type
    //     usageCount: 2,
    //   },
    //   // ... more items
    // ]
    const dummyList = [
      {
        serviceType: '商品',
        selectedProduct: '商品名称商品名称商品名称',
        selectedSku: '短款 XL 白色',
        usageCount: 2,
      },
      {
        serviceType: '第三方服务',
        selectedProduct: '服务名称服务名称服务名称服务名称',
        selectedSku: '',
        usageCount: 2,
      },
       {
        serviceType: '在线咨询',
        selectedProduct: '', // Or serviceName
        selectedSku: '', // Not applicable for this type
        usageCount: 2,
      },
       {
        serviceType: '商品',
        selectedProduct: '另一个商品名称',
        selectedSku: '长款 S 黑色',
        usageCount: 1,
      },
    ];
    serviceInfoList.value = dummyList;
  } catch (error) {
    console.error('Error fetching service info:', error);
    ElMessage.error('获取服务信息失败');
  }
};

// --- Existing Functions (Keep them) ---

// const optionsmerchants = ref([]); // Keep if needed for other dialogs/forms
// const selectedValuemerchants = ref(''); // Keep if needed for other dialogs/forms
const formRef = ref(null) // Keep


// Keep getOrderList if it's used elsewhere, but the merchant selection
// shown in the image is part of the _basic info_ display now, not the form.
// If you still need to *select* a merchant in the basic info tab, you'll
// need optionsmerchants and getOrderList, and bind it to an el-select.
// const getOrderList = () => { /* ... keep implementation ... */ }


// Keep getDetail, but update it to call the new fetch functions
const getDetail = async (id) => {
  // Use the passed id to fetch data for both tabs
  await fetchBasicInfo(id);
  await fetchServiceInfo(id);
  // If you still need to populate state.ruleForm for the original form fields
  // that are hidden now, add that logic here based on the fetched data.
  // For example, if the original form fields are subsets of the basic/service info:
  // state.ruleForm.merchantName = basicInfoData.servicePackageName; // Example mapping
  // state.ruleForm.goodsBelongMerchantId = basicInfoData.selectedOrganizationId; // Example mapping
}

// Keep file upload functions if they are still relevant for any editing
// scenario in this dialog (e.g., if the basic info image is editable)
// const handleBeforeUpload = (file) => { /* ... keep implementation ... */ }
// const handleUrlSuccess = async (file) => { /* ... keep implementation ... */ }
// const generatePreview = (file) => { /* ... keep implementation ... */ }


// Modified open function to fetch data based on id
const open = async (id, status) => {
  state.visible = true
  console.log('Opening dialog with id:', id, 'status:', status)
  state.id = id;
  state.status = status;
  // Reset data when opening
  Object.assign(basicInfoData, {
    servicePackageName: '',
    selectedOrganization: '',
    servicePackageAmount: 0,
    validityPeriod: '',
    status: '',
    associatedDoctors: [],
    serviceDescriptionImg: '',
    reviewResult: '',
  });
  serviceInfoList.value = [];
  activeTab.value = 'basic'; // Default to basic info tab

  if (id) {
    await getDetail(id);
  } else {
     // Handle case where there's no ID (e.g., adding a new item?)
     // The images suggest this dialog is for viewing/reviewing existing details,
     // so the 'add' type might behave differently or use a different dialog.
     // If 'add' uses this dialog, you'll need to handle that state here.
     console.warn("Dialog opened without an ID. Assuming this is for viewing existing data.");
  }
}

// Keep close function
const close = () => {
  state.visible = false
}

// Keep submitForm function, update logic based on activeTab and new data structure
const submitForm = () => {
  // You'll need to implement the logic here to send the updated
  // basicInfoData (especially reviewResult) back to your API.
  // The 'serviceInfoList' is likely for display only in this context.

  // Example of accessing data to send:
  console.log("Submitting review result:", basicInfoData.reviewResult);
  // And any other editable basicInfoData fields if applicable

  // Call your update API
  // axios.put(`/api/update-service-package/${state.id}`, {
  //   reviewResult: basicInfoData.reviewResult,
  //   // Include any other fields that were editable
  // }).then(() => {
  //   ElMessage.success('更新成功');
  //   state.visible = false;
  //   if (props.reload) props.reload();
  // }).catch(error => {
  //   console.error('Update failed:', error);
  //   ElMessage.error('更新失败');
  // });

  // For now, just log and close
  console.log('Submit logic needs to be implemented.');
  state.visible = false;
   if (props.reload) props.reload();
}

// Keep defineExpose
defineExpose({ open, close })

// --- Watchers (Optional) ---
// You might want to watch the activeTab to fetch data specifically when a tab is clicked,
// but fetching all data when the dialog opens is often simpler if the data isn't very large.
// watch(activeTab, (newTab) => {
//   if (state.id) {
//     if (newTab === 'basic') {
//       fetchBasicInfo(state.id);
//     } else if (newTab === 'service') {
//       fetchServiceInfo(state.id);
//     }
//   }
// });

</script>

<style>
/* Styles for dialog header separator */
.el-dialog__header {
  border-bottom: 1px solid #dcdfe6; /* Adjust color/thickness as needed */
  padding-bottom: 15px;
  margin-bottom: 15px;
}
</style>

<style scoped>
/* Add styles for tab buttons */
.tab-buttons {
  margin-bottom: 20px;
}

.basic-info-form, .service-info-list {
  margin-top: 10px;
}

/* Style for doctor tags to allow wrapping */
.doctor-tags {
  display: flex;
  flex-wrap: wrap;
  margin-top: 5px;
  gap: 5px; /* Space between tags */
}

/* Style for each service item in the list */
.service-item {
  margin-bottom: 10px; /* Reduced space between items */
}

/* Reduce margin top/bottom for the divider within service items */
.service-item :deep(.el-divider) {
    margin-top: 10px; /* Adjust top margin */
    margin-bottom: 10px; /* Adjust bottom margin */
}

/* Optional: Add a border or background to service items for clarity */
/*
.service-item {
  border: 1px solid #ebeef5;
  padding: 15px;
  margin-bottom: 15px;
  border-radius: 4px;
}
*/

/* Adjust form item label width if necessary, though default might be fine */
.basic-info-form .el-form-item__label,
.service-item .el-form-item__label {
  /* min-width: 100px; /* Adjust as needed */
}


/* Keep existing styles */
.avatar-uploader {
  width: 100px;
  height: 100px;
  color: #ddd;
  font-size: 30px;
}
.avatar-uploader >>> .el-upload {
  width: 100%;
  text-align: center;
}
.avatar-uploader-icon {
  display: block;
  width: 100%;
  height: 100%;
  border: 1px solid #e9e9e9;
  padding: 32px 17px;
}

/* If you kept the custom-dialog class, use it here instead of targeting .el-dialog */
/*
.custom-dialog :deep(.el-dialog__header) {
  border-bottom: 1px solid #dcdfe6;
  padding-bottom: 15px;
  margin-bottom: 15px;
}
*/

/* Add styles to center the buttons */
.centered-buttons-item :deep(.el-form-item__content) {
  justify-content: center; /* Center content horizontally */
  margin-left: 0 !important; /* Remove default left margin */
}

.centered-buttons-item .dialog-footer {
    display: flex; /* Use flexbox for the button span */
    gap: 20px; /* Add space between buttons */
}
</style>
<template>
  <el-dialog
    v-model="state.visible"
    width="600px"
    :show-close="false"
    :show-header="false"
    center
  >
    <div class="dialog-title">编辑供应商</div>
    
    <el-form 
      :model="state.ruleForm" 
      :rules="state.rules" 
      ref="formRef" 
      label-width="120px" 
      class="supplier-form"
    >
      <el-form-item label="供应商名称" prop="supplierName">
        <el-input 
          v-model="state.ruleForm.supplierName" 
          placeholder="请输入供应商名称"
        />
      </el-form-item>
      
      <el-form-item label="供应商手机号" prop="supplierPhone">
        <el-input 
          v-model="state.ruleForm.supplierPhoneNo" 
          placeholder="请输入供应商手机号"
        />
        <div class="form-tip">供应商手机号作为供应商登录账号</div>
      </el-form-item>
      <el-form-item label="供应商户" prop="merchantNames">
        <div class="merchant-list">
          <div 
            v-for="(merchant, index) in state.ruleForm.merchantNames" 
            :key="index"
            class="merchant-item"
          >
            <el-select
              v-model="state.ruleForm.merchantNames[index]"
              placeholder="请选择供应商户"
              style="flex: 1;"
            >
              <el-option
                v-for="option in state.merchantOptions"
                :key="option.value"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
            <el-button 
              v-if="state.ruleForm.merchantNames.length > 1"
              @click="removeMerchant(index)"
              class="remove-btn"
            >
              -
            </el-button>
            <el-button 
              v-if="index === state.ruleForm.merchantNames.length - 1"
              @click="addMerchant"
              class="add-btn"
            >
              +
            </el-button>
          </div>
        </div>
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-select v-model="state.ruleForm.status" placeholder="请选择状态">
          <el-option label="上架" :value="1" />
          <el-option label="下架" :value="0" />
        </el-select>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import axios from '@/utils/axios'

const props = defineProps({
  type: String,
  reload: Function
})

const formRef = ref(null)
const state = reactive({
  visible: false,
  supplierId: null,
  ruleForm: {
    supplierName: '',
    supplierPhone: '',
    merchantNames: [''],
    status: 1
  },
  rules: {
    supplierName: [
      { required: true, message: '供应商名称不能为空', trigger: 'blur' }
    ],
    supplierPhoneNo: [
      { required: true, message: '供应商手机号不能为空', trigger: 'blur' },
      { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
    ],
    merchantNames: [
      { required: true, message: '供应商户不能为空', trigger: 'change' }
    ],
    status: [
      { required: true, message: '状态不能为空', trigger: 'change' }
    ]
  },
  merchantOptions: [
  ]
})

const getStatusLabel = (status) => {
  return status === 1 ? '开启' : '关闭'
}
// // 获取供应商详情
// const getSupplierDetail = (id) => {
//   try {
//     const params = {
//       supplierName: state.supplierName || undefined,
//       status: state.status !== '' ? state.status : undefined,
//       pageNumber: state.currentPage,
//       pageSize: state.pageSize
//     }
//     const res =  axios.get('/supplier/list', { params })
//     console.log(res,'res.data')
//     state.tableData = res.list || []
//     state.total = res.totalCount || 0
//   } catch (error) {
//     console.error('获取供应商列表失败:', error)
//     ElMessage.error('获取供应商列表失败')
//   } finally {
//     state.loading = false
//   }
// }

// 添加商户选项
const addMerchant = () => {
  state.ruleForm.merchantNames.push('')
}

// 移除商户选项
const removeMerchant = (index) => {
  if (state.ruleForm.merchantNames.length > 1) {
    state.ruleForm.merchantNames.splice(index, 1)
  }
}

// 打开对话框
const open = (row) => {
  state.visible = true
  state.data = row
  //获取merchants
  axios.get('/merchants?pageNumber=1&pageSize=100')
    .then(response => {
      const merchants = response.list
      state.merchantOptions = merchants.map(merchant => ({
        label: merchant.merchantName,
        value: merchant.merchantName
      }))
    })
    .catch(error => {
      console.error('获取商户列表失败:', error)
    })

  if (row) {
    state.supplierId = row.supplierId
    state.ruleForm.supplierName = row.supplierName
    state.ruleForm.supplierPhoneNo = row.supplierPhoneNo
    state.ruleForm.merchantNames = row.merchantNames ? JSON.parse(JSON.stringify(row.merchantNames)) : [''];
    state.ruleForm.status = Number(row.status);
  } else {
    state.ruleForm.supplierName = ''
    state.ruleForm.supplierPhoneNo = ''
    state.ruleForm.merchantNames = ['']
    state.ruleForm.status = ''
  }
  
  // 清除验证
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 关闭对话框
const close = () => {
  state.visible = false
  state.supplierId = null
}

// 取消
const handleCancel = () => {
  close()
}

// 确定
const handleConfirm = async () => {
  formRef.value.validate(async (valid) => {
    if (valid) {
      const validMerchants = state.ruleForm.merchantNames.filter(merchant => merchant.trim() !== '')
      
      if (validMerchants.length === 0) {
        ElMessage.error('请至少选择一个供应商户')
        return
      }
      
      const submitData = {
        supplierId: state.supplierId.toString(),
        supplierName: state.ruleForm.supplierName,
        supplierPhoneNo: state.ruleForm.supplierPhoneNo,
        merchantLists: validMerchants,
        status: state.ruleForm.status.toString()
      }
      
      try {
        await axios.post('/updateSupplier', submitData)
        ElMessage.success('更新成功')
        close()
        if (props.reload) {
          props.reload()
        }
      } catch (error) {
        console.error('更新供应商失败:', error)
        ElMessage.error('更新供应商失败')
      }
    }
  })
}

defineExpose({ open, close })
</script>

<style scoped>
.dialog-title {
  text-align: center;
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.supplier-form {
  padding: 0 20px;
}

.form-tip {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 5px;
}

.merchant-list {
  width: 100%;
}

.merchant-item {
  display: flex;
  align-items: center;
  margin-bottom: 10px;
  gap: 10px;
}

.merchant-item:last-child {
  margin-bottom: 0;
}

.add-btn, .remove-btn {
  width: 32px;
  height: 32px;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  font-weight: bold;
}

.add-btn {
  background-color: #409eff;
  color: white;
  border: none;
}

.add-btn:hover {
  background-color: #66b1ff;
}

.remove-btn {
  background-color: #f56c6c;
  color: white;
  border: none;
}

.remove-btn:hover {
  background-color: #f78989;
}

.dialog-footer {
  text-align: center;
  padding: 20px 0 10px;
}

.dialog-footer .el-button {
  width: 100px;
  margin: 0 10px;
}
</style>

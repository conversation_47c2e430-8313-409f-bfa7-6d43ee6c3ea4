import { fetchUserMenus, transformMenusToRoutes } from '@/utils/permission'

/**
 * 动态路由管理器
 */
class DynamicRouteManager {
    constructor() {
        this.hasAddedRoutes = false
        this.dynamicRoutes = []
    }

    /**
     * 生成动态路由
     * @param {Object} router Vue Router实例
     * @returns {Promise<Array>} 动态路由数组
     */
    async generateRoutes(router) {
        try {
            // 获取用户菜单权限
            const userMenus = await fetchUserMenus()
            console.log(userMenus,'userMenus')
            if (!userMenus || userMenus.length === 0) {
                console.warn('用户没有菜单权限');
                router.push('/login');
                return null;
            }

            // 将菜单数据转换为路由配置
            const dynamicRoutes = transformMenusToRoutes(userMenus)
            
            // 添加动态路由到路由器
            this.addRoutesToRouter(router, dynamicRoutes)
            
            this.dynamicRoutes = dynamicRoutes
            this.hasAddedRoutes = true
            
            return dynamicRoutes
        } catch (error) {
            console.error('生成动态路由失败:', error)
            router.push('/login')
            return null;
        }
    }

    /**
     * 将路由添加到路由器
     * @param {Object} router Vue Router实例
     * @param {Array} routes 路由配置数组
     */
    addRoutesToRouter(router, routes) {
        routes.forEach(route => {
            try {
                router.addRoute(route)
                console.log(`添加动态路由: ${route.path}`)
            } catch (error) {
                console.error(`添加路由失败: ${route.path}`, error)
            }
        })
    }

    /**
     * 重置动态路由
     * @param {Object} router Vue Router实例
     */
    resetRoutes(router) {
        // 由于Vue Router 4没有直接的清除路由方法，
        // 我们需要重新创建路由器实例或者使用其他方法
        this.hasAddedRoutes = false
        this.dynamicRoutes = []
        
        // 这里可以根据需要实现路由重置逻辑
        console.log('动态路由已重置')
    }

    /**
     * 检查是否已添加动态路由
     * @returns {boolean}
     */
    hasRoutes() {
        return this.hasAddedRoutes
    }

    /**
     * 获取动态路由
     * @returns {Array}
     */
    getRoutes() {
        return this.dynamicRoutes
    }
}

// 创建单例实例
const dynamicRouteManager = new DynamicRouteManager()

export default dynamicRouteManager

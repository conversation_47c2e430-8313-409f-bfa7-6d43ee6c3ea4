<template>
    <div class="login-box">
        <div class="login-cont">
            <div class="login-body">
                <div class="login-container">
                    <div class="head">
                        <!-- <img class="logo" src="https://s.yezgea02.com/1582958061265/mlogo.png" /> -->
                        <div class="name">
                            <div class="title">健康商城</div>
                            <!-- <div class="tips">Vue3.0 后台管理系统</div> -->
                        </div>
                    </div>
                    <el-form
                        label-position="top"
                        :rules="state.rules"
                        :model="state.ruleForm"
                        ref="loginForm"
                        class="login-form"
                    >
                        <el-form-item
                            label="账号"
                            prop="username"
                            style="margin-bottom: 30px"
                        >
                            <el-input
                                type="text"
                                v-model.trim="state.ruleForm.username"
                                autocomplete="off"
                            ></el-input>
                        </el-form-item>
                        <el-form-item
                            label="密码"
                            prop="password"
                            style="margin-bottom: 30px"
                        >
                            <el-input
                                type="password"
                                v-model.trim="state.ruleForm.password"
                                autocomplete="off"
                            ></el-input>
                        </el-form-item>
                        <el-form-item>
                            <!-- <div style="color: #333">
                        登录表示您已同意<a>《服务条款》</a>
                    </div> -->
                            <el-button
                                style="
                                    width: 100%;
                                    margin-bottom: 30px;
                                    height: 45px;
                                    font-size: 18px;
                                "
                                type="primary"
                                @click="submitForm"
                                >立即登录</el-button
                            >
                            <el-checkbox
                                v-model="state.checked"
                                @change="!state.checked"
                                >下次自动登录</el-checkbox
                            >
                        </el-form-item>
                    </el-form>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import axios from "@/utils/axios";
import md5 from "js-md5";
import { reactive, ref } from "vue";
import { localSet } from "@/utils";
import { clearUserPermissions } from "@/utils/permission";
import { useRouter } from 'vue-router';
import dynamicRouteManager from '@/router/dynamicRoutes';

const router = useRouter(); // 在顶层作用域获取router实例
const loginForm = ref(null);
const state = reactive({
    ruleForm: {
        username: "",
        password: "",
    },
    checked: true,
    rules: {
        username: [
            { required: "true", message: "账户不能为空", trigger: "blur" },
        ],
        password: [
            { required: "true", message: "密码不能为空", trigger: "blur" },
        ],
    },
});

const submitForm = async () => {
    loginForm.value.validate((valid) => {
        if (valid) {
            clearUserPermissions(); // 确保这里会清除旧的userInfo

            axios
                .post("/adminUser/login", {
                    userName: state.ruleForm.username || "",
                    passwordMd5: md5(state.ruleForm.password),
                })
                .then(async (res) => {
                    localSet("token", res);
                    // 新增：在跳转前获取用户信息并存储
                    try {
                        const userInfo = await axios.get("/adminUser/profile");
                        if (userInfo) {
                            localSet('userInfo', userInfo); // 存储到localStorage
                        }
                    } catch (error) {
                        console.error("登录后获取用户信息失败:", error);
                        localRemove('userInfo'); // 获取失败则清除，避免脏数据
                    }
                    // 生成动态路由
                    await dynamicRouteManager.generateRoutes(router);
                    // 使用router实例进行导航
                    router.push('/introduce');
                }).catch(err => {
                    console.error("登录请求失败:", err);
                    // 可以在这里添加登录失败的用户提示
                });
        } else {
            console.log("error submit!!");
            return false;
        }
    });
};
</script>

<style scoped>
.login-box {
    width: 100%;
    height: 100%;
    background: url("@/assets/login_bg.png") no-repeat;
    background-size: cover;
    /* display: flex;
    align-items: center;
    justify-content: center; */
}
.login-box:before {
    width: 100%;
    height: 100%;
    position: absolute;
    content: "";
    background: rgba(22, 52, 24, 0.6);
}
.login-cont {
    width: 100%;
    height: 100%;
    background: transparent;
    display: flex;
    justify-content: center;
    align-items: center;
}
.login-body {
    display: flex;
    justify-content: center;
    align-items: center;
    /* width: 100%; */
    background-color: #fff;
    border-radius: 16px;
    z-index: 1;
}
.login-container {
    width: 420px;
    height: 500px;
    background-color: #fff;
    border-radius: 16px;
    /* border-radius: 4px; */
    /* box-shadow: 0px 21px 41px 0px rgba(0, 0, 0, 0.2); */
}
.head {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 40px 0 20px 0;
}
.head img {
    width: 100px;
    height: 100px;
    margin-right: 20px;
}
.head .title {
    font-size: 28px;
    color: #1baeae;
    font-weight: bold;
}
.head .tips {
    font-size: 12px;
    color: #999;
}
.login-form {
    width: 70%;
    margin: 0 auto;
}
.login-form >>> .el-form--label-top .el-form-item__label {
    padding: 0;
}
.login-form >>> .el-form-item {
    margin-bottom: 0;
}
</style>
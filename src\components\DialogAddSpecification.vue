<template>
  <el-dialog
    :title="props.type === 'add' ? '添加商品规格' : '修改商品规格'"
    v-model="state.visible"
    width="1000px"
  >
    <el-form :model="state.ruleForm" ref="formRef" label-width="100px" class="specification-form">
      <el-form-item label="* 规格名称">
        <el-input type="text" v-model="state.ruleForm.specName" placeholder="请输入规格名称"></el-input>
      </el-form-item>

      <!-- 规格和属性动态表单 -->
      <div v-for="(spec, specIndex) in state.specs" :key="'spec-' + specIndex" >
        <div class="spec-container">
          <!-- <el-form-item :label="specIndex === 0 ? '规格类型' : ''" :prop="'specs[' + specIndex + '].name'" :rules="{ required: true, message: '规格类型不能为空', trigger: 'blur' }">
            <el-input v-model="spec.name" :placeholder="specIndex === 0 ? '请输入规格类型' : '请输入规格类型'"></el-input>
          </el-form-item> -->

          <!-- 属性类型和属性值 -->
          <div v-for="(attrType, typeIndex) in spec.attrTypes" :key="'type-' + specIndex + '-' + typeIndex" class="attr-type-container">
            <el-form-item label="规格">
              <el-input v-model="attrType.name" placeholder="请输入规格"></el-input>
            </el-form-item>

            <!-- 属性值列表 -->
            <div v-for="(attr, attrIndex) in attrType.attrs" :key="'attr-' + specIndex + '-' + typeIndex + '-' + attrIndex" class="attr-item">
              <el-form-item :label="attrIndex === 0 ? '属性' : ''">
                <div class="attr-input-container">
                  <el-input v-model="attr.value" placeholder="请输入属性"></el-input>
                  <el-input
                    v-if="attr.id !== undefined"
                    v-model="attr.id"
                    type="hidden"
                  ></el-input>
                  <el-button
                    type="primary"
                    class="add-attr-btn"
                    @click="addAttrValue(specIndex, typeIndex)"
                    v-if="attrIndex === attrType.attrs.length - 1"
                  >
                    增加属性值
                  </el-button>
                </div>
              </el-form-item>
            </div>
          </div>

          <!-- 添加新属性类型按钮 -->
          <!-- <div class="add-attr-type-btn-container">
            <el-button type="success" @click="addAttrType(specIndex)">添加属性类型</el-button>
          </div> -->
                <!-- 添加新规格按钮 -->
          <div class="add-spec-btn-container">
            <el-button type="primary" @click="addAttrType(specIndex)">添加属性类型</el-button>
          </div>
        </div>
      </div>


    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="close">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { addSpecification, updateSpecification } from '@/api/specification'

const props = defineProps({
  type: String, // 用于判断是添加还是编辑
  reload: Function // 添加或修改完后，刷新列表页
})

const emits = defineEmits(['success', 'close'])

const formRef = ref(null)
const state = reactive({
  visible: false,
  ruleForm: {
    specName: '',
  },

  specs: [
    // 默认添加一个规格和一个属性类型
    {
      name: '',
      attrTypes: [
        {
          name: '',
          attrs: [{ value: ''}]
        }
      ]
    }
  ],
  originalData: null // 保存原始数据，用于编辑时
})

// 添加新规格
// const addSpec = () => {
//   state.specs.push({
//     name: '',
//     attrTypes: [
//       {
//         name: '',
//         attrs: [{ value: ''}]
//       }
//     ]
//   })
// }

// 添加属性类型
const addAttrType = (specIndex) => {
  state.specs[specIndex].attrTypes.push({
    name: '',
    attrs: [{ value: '' }]
  })
}

// 添加属性值
const addAttrValue = (specIndex, typeIndex) => {
  state.specs[specIndex].attrTypes[typeIndex].attrs.push({ value: ''})
}

// 解析编辑数据
const parseEditData = (data) => {
  // console.log('解析编辑数据:', data)
  if (!data) {
    console.error('编辑数据为空')
    return
  }


  // 重置规格数组
  state.specs = []

  // 处理新的数据结构
  if (data.param && data.param.name && Array.isArray(data.param.specAttr)) {
    // 新的数据结构
    const specName = data.param.name
    const specAttrArray = data.param.specAttr

    // 设置规格名称
    state.ruleForm.specName = specName

    // 创建一个映射来存储不同规格类型的属性
    const specTypeMap = {}

    // 遍历所有规格属性对
    specAttrArray.forEach(specAttrStr => {
      // 分割规格类型和属性值
      const parts = specAttrStr.split('-')
      if (parts.length >= 2) {
        const specType = parts[0]
        const attrValue = parts.slice(1).join('-') // 处理属性值中可能包含'-'的情况

        // 如果这个规格类型还没有添加到映射中，则初始化
        if (!specTypeMap[specType]) {
          specTypeMap[specType] = []
        }

        // 添加属性值
        specTypeMap[specType].push({
          value: attrValue
        })
      }
    })

    // 构建规格对象
    const spec = {
      name: specName,
      attrTypes: []
    }

    // 将映射转换为规格对象的属性类型数组
    for (const specType in specTypeMap) {
      spec.attrTypes.push({
        name: specType,
        attrs: specTypeMap[specType]
      })
    }

    state.specs.push(spec)
  } else {
    // 旧的数据结构，保持原有逻辑
    // 遍历规格数据
    for (const specName in data) {

      const specData = data[specName]
      // console.log('处理规格:', specName, specData)

      const spec = {
        name: specName,
        attrTypes: []
      }

      // 遍历该规格下的所有属性类型
      for (const attrTypeName in specData) {
        // console.log('处理属性类型:', attrTypeName, specData[attrTypeName])

        const attrType = {
          name: attrTypeName,
          attrs: []
        }

        // 遍历该属性类型下的所有属性值
        specData[attrTypeName].forEach(attr => {
          attrType.attrs.push({
            value: attr.attribute,
            id: attr.id,
          })
        })

        spec.attrTypes.push(attrType)
      }

      state.specs.push(spec)
    }
  }

  // 如果没有规格，添加一个默认的空规格
  if (state.specs.length === 0) {
    console.warn('没有找到有效的规格数据，使用默认空规格')
    state.specs = [{
      name: '',
      attrTypes: [
        {
          name: '',
          attrs: [{ value: '' }]
        }
      ]
    }]
  } else {
    // console.log('解析后的规格数据:', state.specs)
  }
}

// 开启弹窗
const open = (data) => {
  // console.log('打开对话框，接收数据:', data)
  state.originalData = data
  state.visible = true

  // 重置表单
  state.ruleForm.specName = ''
  state.specs = [{
    name: '',
    attrTypes: [
      {
        name: '',
        attrs: [{ value: '' }]
      }
    ]
  }]

  if (data) {
    try {
      // 编辑模式，解析传入的数据
      parseEditData(data)

      // 如果是新的数据结构，规格名称已经在parseEditData中设置
      if (!(data.param && data.param.name)) {
        // 旧的数据结构，设置规格名称（取第一个规格的名称作为整体规格名称）
        const specNames = Object.keys(data).filter(key => key !== 'sequence')
        if (specNames.length > 0) {
          state.ruleForm.specName = specNames[0]
          // console.log('设置规格名称:', state.ruleForm.specName)
        }
      }
    } catch (error) {
      console.error('解析编辑数据时出错:', error)
      ElMessage.error('加载规格数据失败')
    }
  }
}

// 关闭弹窗
const close = () => {
  state.visible = false
  emits('close')
}

// 提交表单
const submitForm = () => {
  // 过滤掉空的规格和属性
  const validSpecs = state.specs.filter(spec => {
    // 检查是否有有效的属性类型
    const validAttrTypes = spec.attrTypes.filter(attrType => attrType.name.trim() !== '')
    return validAttrTypes.length > 0
  })

  // 构建提交数据
  if (props.type === 'add') {
    // 添加规格 - 使用新的 AddSpecificationParam 格式
    const submitData = {
      name: state.ruleForm.specName,
      specAttr: []
    }

    // 遍历所有有效的规格和属性，构建 specAttr 数组
    validSpecs.forEach(spec => {
      spec.attrTypes.forEach(attrType => {
        if (attrType.name.trim() === '') return

        attrType.attrs.forEach(attr => {
          if (attr.value.trim() === '') return

          // 添加规格和属性对，格式为 "规格名称-属性值"
          const specAttrString = `${attrType.name}-${attr.value}`;
          submitData.specAttr.push(specAttrString);
        })
      })
    })

    if (submitData.specAttr.length === 0) {
      ElMessage.error('请至少添加一个规格和属性')
      return
    }

    // 检查规格名称
    if (!state.ruleForm.specName) {
      ElMessage.error('规格名称不能为空')
      return
    }

    // 提交添加请求
    addSpecification(submitData).then(() => {
      ElMessage.success('添加成功')
      state.visible = false
      emits('success')
    }).catch(err => {
      console.error('添加失败:', err)
      ElMessage.error('添加失败')
    })
  } else {
    // 编辑规格 - 使用新的 UpdateSpecificationParam 格式

    // 检查规格名称
    if (!state.ruleForm.specName) {
      ElMessage.error('规格名称不能为空')
      return
    }


    // 构建 specAttr 数组
    const specAttr = [];

    // 遍历所有有效的规格和属性，构建 specAttr 数组
    validSpecs.forEach(spec => {
      spec.attrTypes.forEach(attrType => {
        if (attrType.name.trim() === '') return;

        attrType.attrs.forEach(attr => {
          if (attr.value.trim() === '') return;

          // 添加规格和属性对，格式为 "规格名称-属性值"
          const specAttrString = `${attrType.name}-${attr.value}`;
          specAttr.push(specAttrString);
        });
      });
    });

    // 检查是否有规格和属性
    if (specAttr.length === 0) {
      ElMessage.error('请至少添加一个规格和属性')
      return
    }

    // 构建最终的更新数据
    const updateData = {
      param: {
        name: state.ruleForm.specName,
        specAttr: specAttr
      }
    };

    // console.log('更新规格数据:', updateData);

    // 提交更新请求
    updateSpecification(updateData).then(() => {
      ElMessage.success('修改成功');
      state.visible = false;
      emits('success');
    }).catch(err => {
      console.error('修改失败:', err);
      ElMessage.error('修改失败');
    });
  }
}

defineExpose({ open, close })
</script>

<style scoped>
.specification-form {
  margin-top: 20px;
}

.spec-item {
  margin-bottom: 20px;
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 15px;
  background-color: #f9f9f9;
}

.attr-type-container {
  margin-bottom: 15px;
  padding: 15px;
  background-color: #ffffff;
  border: 1px dashed #a0cfff;
  border-radius: 4px;
}

.attr-item {
  margin-bottom: 10px;
}

.attr-input-container {
  display: flex;
  align-items: center;
}

.attr-input-container .el-input {
  margin-right: 10px;
  flex: 1;
}

.add-attr-btn {
  width: 120px;
}

.add-attr-type-btn-container {
  margin-top: 10px;
  margin-bottom: 10px;
  display: flex;
  justify-content: flex-end;
}

.add-spec-btn-container {
  margin-top: 20px;
  margin-bottom: 20px;
  display: flex;
  justify-content: flex-start;
}


</style>

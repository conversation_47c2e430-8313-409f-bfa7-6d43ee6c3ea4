<template>
    <el-menu
        background-color="#222832"
        text-color="#fff"
        :router="true"
        :default-openeds="defaultOpeneds"
        :default-active="currentPath"
        class="dynamic-menu"
    >
        <template v-for="menu in visibleMenus" :key="menu.id">
            <!-- 有子菜单的情况 -->
            <el-sub-menu 
                v-if="menu.children && menu.children.length > 0"
                :index="String(menu.id)"
            >
                <template #title>
                    <el-icon v-if="menu.icon">
                        <component :is="getIconComponent(menu.icon)" />
                    </el-icon>
                    <span>{{ menu.title }}</span>
                </template>
                
                <el-menu-item-group>
                    <template v-for="child in menu.children" :key="child.id">
                        <el-menu-item 
                            :index="child.path || `/${child.name}`"
                        >
                            <el-icon v-if="child.icon">
                                <component :is="getIconComponent(child.icon)" />
                            </el-icon>
                            {{ child.title }}
                        </el-menu-item>
                    </template>
                </el-menu-item-group>
            </el-sub-menu>
            
            <!-- 没有子菜单的情况 -->
            <el-menu-item 
                v-else-if="!menu.hidden"
                :index="menu.path || `/${menu.name}`"
            >
                <el-icon v-if="menu.icon">
                    <component :is="getIconComponent(menu.icon)" />
                </el-icon>
                <span>{{ menu.title }}</span>
            </el-menu-item>
        </template>
    </el-menu>
</template>

<script setup>
import { computed, ref, watch } from 'vue'
import { useRoute } from 'vue-router'
import { getUserMenus } from '@/utils'
// 导入Element Plus图标
import {
    Menu,
    List,
    User,
    Lock,
    Odometer,
    Setting,
    ShoppingCart,
    Document,
    Goods,
    Shop,
    Money,
    DataAnalysis
} from '@element-plus/icons-vue'

const route = useRoute()

// 图标映射
const iconMap = {
    'menu': Menu,
    'list': List,
    'user': User,
    'lock': Lock,
    'odometer': Odometer,
    'setting': Setting,
    'shopping-cart': ShoppingCart,
    'document': Document,
    'goods': Goods,
    'shop': Shop,
    'money': Money,
    'data-analysis': DataAnalysis,
    'editor': Document // 默认图标
}

// 获取图标组件
const getIconComponent = (iconName) => {
    return iconMap[iconName] || iconMap['editor']
}

// 当前路径
const currentPath = ref(route.path)

// 监听路由变化
watch(() => route.path, (newPath) => {
    currentPath.value = newPath
})

// 获取用户菜单数据
const userMenus = computed(() => {
    return getUserMenus()
})

// 过滤可见菜单
const visibleMenus = computed(() => {
    const menus = userMenus.value
    if (!menus || menus.length === 0) {
        return []
    }
    
    // 这里可以根据用户角色进一步过滤菜单
    // 暂时返回所有菜单
    return menus.filter(menu => !menu.hidden)
})

// 默认展开的菜单
const defaultOpeneds = computed(() => {
    return visibleMenus.value
        .filter(menu => menu.children && menu.children.length > 0) // 只选择有子菜单的项
        .map(menu => String(menu.id)); // 将id转为字符串
});

// 暴露给父组件的方法
defineExpose({
    refreshMenus: () => {
        // 刷新菜单数据的方法
        console.log('刷新菜单数据')
    }
})
</script>

<style scoped>
.dynamic-menu {
    border: none;
}

.dynamic-menu .el-menu-item {
    color: #fff;
}

.dynamic-menu .el-menu-item:hover {
    background-color: #333;
}

.dynamic-menu .el-menu-item.is-active {
    background-color: #409EFF;
}

.dynamic-menu .el-sub-menu__title {
    color: #fff;
}

.dynamic-menu .el-sub-menu__title:hover {
    background-color: #333;
}
</style>

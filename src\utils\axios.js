import axios from 'axios'
import { ElMessage } from 'element-plus'
import router from '@/router/index'
import { localGet } from './index'
import config from '~/config'



// 这边由于后端没有区分测试和正式，姑且都写成一个接口。
axios.defaults.baseURL = config[import.meta.env.MODE].baseUrl
// 携带 cookie，对目前的项目没有什么作用，因为我们是 token 鉴权
axios.defaults.withCredentials = true
// 请求头，headers 信息
axios.defaults.headers['X-Requested-With'] = 'XMLHttpRequest'
axios.defaults.headers['token'] = localGet('token') || ''
console.log("token", localGet('token'))
// 默认 post 请求，使用 application/json 形式
axios.defaults.headers.post['Content-Type'] = 'application/json'


axios.interceptors.request.use(config => {
    // 根据需要修改config.headers

    config.headers['token'] = localGet('token') || '';
    return config;
}, error => {
    return Promise.reject(error);
});

// 请求拦截器，内部根据返回值，重新组装，统一管理。
axios.interceptors.response.use(res => {
    if (typeof res.data !== 'object') {
        console.log("typeof res.data", typeof res.data)
        ElMessage.error('服务端异常！')
        return Promise.reject(res)
    }
    if (res.data.resultCode != 200) {
        if (res.data.message) ElMessage.error(res.data.message)
        if (res.data.resultCode == 419) {
            // 仅使用 router.push 进行跳转
            router.push({ path: '/login' });
        }
        return Promise.reject(res.data)
    }

    // 如果 data 字段为 null，则返回一个空对象，避免 null.data 错误
    return res.data.data !== null ? res.data.data : {}
})

export default axios
<template>
  <div class="category-page">
    <el-button type="primary" :icon="Plus" @click="handleAddCategory" style="margin-bottom: 10px;">添加商品分类</el-button>
    <el-alert title="平台分类可设置二级分类" type="warning" :closable="false" style="margin-bottom: 20px;"></el-alert>

    <el-table
      v-loading="state.loading"
      :data="state.tableData"
      style="width: 100%"
      row-key="categoryId"
      :tree-props="{ children: 'childrenList' }"

    >
      <el-table-column prop="categoryName" label="名称" width="250">
      </el-table-column>
      <el-table-column label="分类图片" width="150" align="center">
        <template #default="scope">
          <el-image
            style="width: 40px; height: 40px"
            :src="scope.row.categoryImg || ''"
            fit="cover">
             <template #error>
              <div class="image-slot" style="display: flex; justify-content: center; align-items: center; width: 100%; height: 100%; background: #f5f7fa; color: #909399;">
                <el-icon><Picture /></el-icon>
              </div>
            </template>
          </el-image>
        </template>
      </el-table-column>
      <el-table-column prop="categoryRank" label="排序" width="100" align="center">
      </el-table-column>
      <el-table-column label="状态" width="100" align="center">
        <template #default="scope">
          <el-switch
            v-model="scope.row.status"
            :active-value="true"
            :inactive-value="false"
            @change="(val) => handleStatusChange(scope.row.categoryId, val)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button type="primary" link v-if="scope.row.categoryLevel === 1" @click="handleAddSubCategory(scope.row)" style="padding: 0 5px;">添加子级分类</el-button>
          <el-button type="primary" link @click="handleEdit(scope.row)" style="padding: 0 5px;">编辑</el-button>
          <el-button type="danger" link style="padding: 0 5px;" @click="showDeleteConfirm(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div style="display: flex; justify-content: flex-end; margin-top: 20px;">
      <el-pagination
        background
        layout="prev, pager, next, total"
        :total="state.total"
        :page-size="state.pageSize"
        :current-page="state.currentPage"
        @current-change="changePage"
      >
      </el-pagination>
    </div>
  </div>

  <!-- 添加一级分类弹窗 -->
  <DialogAddCategory ref="dialogAddCategoryRef" :type="state.dialogType" :reload="getCategoryList" />

  <!-- 添加子级分类弹窗 -->
  <DialogAddSubCategory ref="dialogAddSubCategoryRef" :type="state.dialogType" :reload="getCategoryList" />

  <!-- 自定义确认删除弹窗 -->
  <CustomConfirmDialog
    ref="confirmDialogRef"
    title=""
    message="是否删除该分类?"
    confirm-text="是"
    cancel-text="否"
    @confirm="confirmDelete"
  />
</template>

<script setup>
import { reactive, onMounted, ref } from 'vue';
import { ElMessage, ElButton, ElAlert, ElTable, ElTableColumn, ElImage, ElSwitch, ElPagination, ElIcon } from 'element-plus';
import { Plus, Picture } from '@element-plus/icons-vue';
import DialogAddCategory from '@/components/DialogAddCategory.vue';
import DialogAddSubCategory from '@/components/DialogAddSubCategory.vue';
import CustomConfirmDialog from '@/components/CustomConfirmDialog.vue';
import { getCategoryList as fetchCategoryList, deleteCategory, updateCategoryStatus } from '@/api/category';

// 引用弹窗组件
const dialogAddCategoryRef = ref(null);
const dialogAddSubCategoryRef = ref(null);
const confirmDialogRef = ref(null);

// 存储待删除的分类ID
const categoryIdToDelete = ref(null);

// 状态数据
const state = reactive({
  loading: false,
  tableData: [],
  total: 0,
  currentPage: 1,
  pageSize: 10,
  dialogType: 'add', // 弹窗类型：add-添加，edit-编辑
});

// 获取分类数据
const getCategoryList = () => {
  state.loading = true;

  // 使用API方法请求后端接口
  fetchCategoryList({
    pageNumber: state.currentPage,
    pageSize: state.pageSize,
    categoryLevel:1
  }).then(res => {

    if (res.list) {
      // 接口返回成功，使用接口返回的数据
      console.log('res.list:', res.list);
      state.tableData = res.list;
      state.total = res.totalCount;
    } else {
      // 其他情况
      ElMessage.error('获取分类数据失败');
      state.tableData = [];
      state.total = 0;
    }

    state.loading = false;
  }).catch(err => {
    console.error('获取分类列表失败', err);
    ElMessage.error('获取分类列表失败');
    state.loading = false;
  });
};

const handleAddCategory = () => {
  state.dialogType = 'add';
  dialogAddCategoryRef.value.open(null, 'add');
  getCategoryList();
};

const handleAddSubCategory = (row) => {
  state.dialogType = 'add';
  dialogAddSubCategoryRef.value.open(null, 'add', row);
  getCategoryList();
};

const handleEdit = (row) => {
  state.dialogType = 'edit';

  // 根据层级决定使用哪个弹窗组件
  if (row.categoryLevel === 1) {
    // 一级分类
    dialogAddCategoryRef.value.open(row.categoryId, 'edit', null, row);
  } else {
    // 子级分类
    dialogAddSubCategoryRef.value.open(row.categoryId, 'edit', null, row);
  }
  getCategoryList();
};

// 显示删除确认弹窗
const showDeleteConfirm = (row) => {
  categoryIdToDelete.value = row.categoryId;
  confirmDialogRef.value.open();
};

// 确认删除
const confirmDelete = () => {
  const id = categoryIdToDelete.value;
  if (!id) return;

  deleteCategory(id).then(() => {
    ElMessage.success('删除成功');
    getCategoryList(); // 重新获取数据
  }).catch(err => {
    console.error('删除分类失败', err);
    ElMessage.error('删除失败');
  });

  // 重置待删除ID
  categoryIdToDelete.value = null;
};

const changePage = (page) => {
  state.currentPage = page;
  getCategoryList(); // 切换页面时重新获取数据
};

// 处理状态切换
const handleStatusChange = (categoryId, status) => {
  updateCategoryStatus({
    categoryId: categoryId,
    status: status
  }).then(() => {
    ElMessage.success('状态修改成功')
    // 成功后刷新数据
    getCategoryList()
  }).catch(() => {
    // 如果失败则回滚状态
    getCategoryList()
    ElMessage.error('状态修改失败')
  })
}

// 组件挂载时获取数据
onMounted(() => {
  getCategoryList();
});
</script>

<style scoped>
.category-page {
  padding: 20px;
}
.el-button--text {
  padding-left: 5px;
  padding-right: 5px;
}
</style>

export function localGet(key) {
    const value = window.localStorage.getItem(key)
    try {
        return JSON.parse(window.localStorage.getItem(key))
    } catch (error) {
        return value
    }
}

export function localSet(key, value) {
    window.localStorage.setItem(key, JSON.stringify(value))
}
export function localRemove(key) {
    window.localStorage.removeItem(key)
}

// 判断内容是否含有表情字符，现有数据库不支持。
export function hasEmoji(str = '') {
    const reg = /[^\u0020-\u007E\u00A0-\u00BE\u2E80-\uA4CF\uF900-\uFAFF\uFE30-\uFE4F\uFF00-\uFFEF\u0080-\u009F\u2000-\u201f\u2026\u2022\u20ac\r\n]/g;
    return str.match(reg) && str.match(reg).length
}

// 单张图片上传
export const uploadImgServer = 'https://jsbceshi.hfi-health.com:18188/health_mall/manage-api/v1/upload/file'
// 多张图片上传
export const uploadImgsServer = 'https://jsbceshi.hfi-health.com:18188/health_mall/manage-api/v1/upload/file'



// 存储和获取用户权限菜单
export function setUserMenus(menus) {
    localSet('userMenus', menus)
}

export function getUserMenus() {
    return localGet('userMenus') || []
}

export function clearUserMenus() {
    localRemove('userMenus')
}
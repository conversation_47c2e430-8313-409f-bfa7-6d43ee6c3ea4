<template>
  <el-card class="order-container">
    <template #header>
      <div class="header">
        <el-input
          style="width: 200px; margin-right: 10px"
          placeholder="请输入服务包名称"
          v-model="state.phoneNo"
          clearable
        />

        <el-select
          v-model="state.organId"
          style="width: 200px; margin-right: 10px"
          placeholder="请选择商户"
        >
          <el-option
            v-for="item in state.merchantList"
            :key="item.merCode"
            :label="item.merchantName"
            :value="item.merCode"
          />
        </el-select>

        <el-select
          v-model="state.belong"
          placeholder="请选择所属供应商"
          style="width: 190px; margin-right: 10px"
        >
          <el-option
            v-for="item in state.supplierList"
            :key="item.supplierId"
            :label="item.supplierName"
            :value="item.supplierId"
          />
        </el-select>

        <el-select
          v-model="state.organId"
          style="width: 200px; margin-right: 10px"
          placeholder="请选择状态"
        >
          <el-option
            v-for="item in state.merchantList"
            :key="item.organId"
            :label="item.organName"
            :value="item.organId"
          />
        </el-select>

        <!-- <el-button type="primary" size="small" icon="el-icon-edit">修改订单</el-button> -->
        <el-button type="primary" @click="handleOption()" style="margin-right: 10px"
          >搜索</el-button
        >
        <el-button type="warning" @click="clearSearch()">重置</el-button>

        <el-button type="danger" @click="addFwb()">服务包配置</el-button>
        <!-- <el-button
                    type="primary"
                    :icon="HomeFilled"
                    @click="handleConfig()"
                    >配货完成</el-button
                >
                <el-button
                    type="primary"
                    :icon="HomeFilled"
                    @click="handleSend()"
                    >出库</el-button
                >
                <el-button type="danger" :icon="Delete" @click="handleClose()"
                    >关闭订单</el-button
                > -->
      </div>
    </template>
    <!-- @selection-change="handleSelectionChange" -->
    <el-table
      :load="state.loading"
      :data="state.tableData"
      tooltip-effect="dark"
      style="width: 100%"
    >
      <!-- <el-table-column type="selection" width="55"> </el-table-column> -->

      <el-table-column type="index" :index="oprIndex" label="序号" width="100px">
      </el-table-column>
      <el-table-column prop="goodsName" label="服务包名称"> </el-table-column>
      <el-table-column prop="goodsBelongMerchantName" label="所属商户"> </el-table-column>
      <el-table-column prop="goodsBelongSupplierName" label="所属供应商">
      </el-table-column>
      <el-table-column prop="goodsCategoryName" label="所属分类"> </el-table-column>
      <el-table-column prop="goodsSellingPrice" label="服务包金额">
        <template #default="scope">
          <span>{{ scope.row.goodsSellingPrice + "元" }}</span>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="serviceType" label="售后类型"> </el-table-column>
      <el-table-column prop="returnType" label="退货类型"> </el-table-column>
      <el-table-column prop="refundStatus" label="退款状态"> </el-table-column>
      <el-table-column prop="createTime" label="创建时间"> </el-table-column> -->
      <!-- <el-table-column prop="orderStatus" label="订单状态">
                <template #default="scope">
                    <span>{{ $filters.orderMap(scope.row.orderStatus) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="payType" label="支付方式">
                <template #default="scope">
                    <span v-if="scope.row.payType == 1">微信支付</span>
                    <span v-else-if="scope.row.payType == 2">支付宝支付</span>
                    <span v-else>未知</span>
                </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间">
            </el-table-column> -->
      <el-table-column label="操作">
        <template #default="scope">
          <el-button type="primary" @click="editFwb(scope.row.goodsId)">详情</el-button>
          <!-- <el-popconfirm
                        v-if="scope.row.orderStatus == 1"
                        title="确定配货完成吗？"
                        @confirm="handleConfig(scope.row.orderId)"
                        confirm-button-text="确定"
                        cancel-button-text="取消"
                    >
                        <template #reference>
                            <a style="cursor: pointer; margin-right: 10px"
                                >配货完成</a
                            >
                        </template>
                    </el-popconfirm>
                    <el-popconfirm
                        v-if="scope.row.orderStatus == 2"
                        title="确定出库吗？"
                        @confirm="handleSend(scope.row.orderId)"
                        confirm-button-text="确定"
                        cancel-button-text="取消"
                    >
                        <template #reference>
                            <a style="cursor: pointer; margin-right: 10px"
                                >出库</a
                            >
                        </template>
                    </el-popconfirm>
                    <el-popconfirm
                        v-if="
                            !(
                                scope.row.orderStatus == 4 ||
                                scope.row.orderStatus < 0
                            )
                        "
                        title="确定关闭订单吗？"
                        @confirm="handleClose(scope.row.orderId)"
                        confirm-button-text="确定"
                        cancel-button-text="取消"
                    >
                        <template #reference>
                            <a style="cursor: pointer; margin-right: 10px"
                                >关闭订单</a
                            >
                        </template>
                    </el-popconfirm>
                    <router-link
                        :to="{
                            path: '/order_detail',
                            query: { id: scope.row.orderId },
                        }"
                        >订单详情</router-link
                    > -->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      layout="prev, pager, next"
      :total="state.total"
      :page-size="state.pageSize"
      :current-page="state.currentPage"
      @current-change="changePage"
    />

    <!-- 服务包配置组件 -->
    <el-dialog v-model="state.dialogVisible" title="服务包配置" width="90%" fullscreen>
      <fwbSetDialog
        ref="fwbSetRef"
        :fwbId="state.fwbId"
        :MerchantList="state.merchantList"
        :supplierList="state.supplierList"
      />
    </el-dialog>

    <!--  -->
  </el-card>
</template>

<script setup>
import { onMounted, reactive, ref, nextTick } from "vue";
import { ElMessage } from "element-plus";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import { HomeFilled, Delete } from "@element-plus/icons-vue";
import axios from "@/utils/axios";
import fwbSetDialog from "./fwbSetDialog.vue";
import { provide } from "vue";
const fwbSetRef = ref(null);
const oprIndex = (index) => {
  // 序号
  let count = (state.currentPage - 1) * state.pageSize + 1 + index;
  return count;
};
const state = reactive({
  fwbId: "",
  dialogVisible: false,
  detail: {},
  createTime: "",
  locale: zhCn,
  loading: false,
  organId: "",
  belong: "",
  typeList: [
    { id: "1", name: "自营" },
    { id: "2", name: "第三方" },
  ],
  merchantList: [], //商户列表
  supplierList: [], //供应商列表
  tableData: [], // 数据列表
  multipleSelection: [], // 选中项
  phoneNo: "",
  refundSeq: "",
  total: 0, // 总条数
  currentPage: 1, // 当前页
  pageSize: 10, // 分页大小
  orderNo: "", // 订单号
  orderStatus: "", // 订单状态
});

const addFwb = (id) => {
  // todo
  state.dialogVisible = true;
  debugger;
  nextTick(() => {
    if (fwbSetRef.value) {
      debugger;
      fwbSetRef.value.initFwb(id || "");
    }
  });
};

const editFwb = (id) => {
  // todo
  console.log("state.fwbIdstate.fwbIdstate.fwbIdstate.fwbId", state.fwbId);

  addFwb(id);
};
const showDetail = (row) => {
  // todo
  console.log("eeee", row);

  // /orders/{orderId}
  state.loading = true;
  axios.get("/orders/" + row.orderId, null).then((res) => {
    console.log(res);
    state.dialogVisible = true;
    state.detail = res;

    state.loading = false;
  });
};
// 初始化获取订单列表
onMounted(() => {
  getList();
  getSysUsersList();
  getSupplierList();
});
const getSpec = (str) => {
  return JSON.parse(str).spec;
};
// 获取列表方法
const getList = () => {
  state.loading = true;
  axios
    .get("/servicePack/list", {
      params: {
        pageNumber: state.currentPage,
        pageSize: state.pageSize,
      },
    })
    .then((res) => {
      state.tableData = res.list;
      state.total = res.totalCount;
      state.currentPage = res.currPage;
      state.loading = false;
    });
};
// 获取商户列表方法
const getSysUsersList = () => {
  state.loading = true;
  axios
    .get("/merchants", {
      params: {
        pageNumber: 1,
        pageSize: 999999,
      },
    })
    .then((res) => {
      state.merchantList = res.list;
      // state.loading = false;
    });
};
// 获取供应商列表
const getSupplierList = () => {
  state.loading = true;
  axios
    .get("/supplier/list", {
      params: {
        pageNumber: 1,
        pageSize: 999999,
      },
    })
    .then((res) => {
      state.supplierList = res.list;
      state.loading = false;
    });
};

// 触发过滤项方法
const handleOption = () => {
  state.currentPage = 1;
  getList();
};
const pMhandleOption = () => {
  state.dialogVisible = false;
  handleOption();
};
provide("pMhandleOption", pMhandleOption); // 使用 provide 暴露方法
const clearSearch = () => {
  state.currentPage = 1;
  state.currentPage = 1;
  state.orderNo = "";
  state.createTime = "";
  state.organId = "";
  state.phoneNo = "";
  state.refundSeq = "";
  state.belong = "";
  getList();
};

// checkbox 选择项
const handleSelectionChange = (val) => {
  state.multipleSelection = val;
};
// 翻页方法
const changePage = (val) => {
  state.currentPage = val;
  getList();
};
// 配货方法
const handleConfig = (id) => {
  let params;
  // 当个配置
  if (id) {
    params = [id];
  } else {
    if (!state.multipleSelection.length) {
      console.log("state.multipleSelection", state.multipleSelection.length);
      ElMessage.error("请选择项");
      return;
    }
    // 多选配置
    params = state.multipleSelection.map((i) => i.orderId);
  }
  axios
    .put("/orders/checkDone", {
      ids: params,
    })
    .then(() => {
      ElMessage.success("配货成功");
      getList();
    });
};
// 出库方法
const handleSend = (id) => {
  let params;
  if (id) {
    params = [id];
  } else {
    if (!state.multipleSelection.length) {
      ElMessage.error("请选择项");
      return;
    }
    params = state.multipleSelection.map((i) => i.orderId);
  }
  axios
    .put("/orders/checkOut", {
      ids: params,
    })
    .then(() => {
      ElMessage.success("出库成功");
      getList();
    });
};
// 关闭订单方法
const handleClose = (id) => {
  let params;
  if (id) {
    params = [id];
  } else {
    if (!state.multipleSelection.length) {
      ElMessage.error("请选择项");
      return;
    }
    params = state.multipleSelection.map((i) => i.orderId);
  }
  axios
    .put("/orders/close", {
      ids: params,
    })
    .then(() => {
      ElMessage.success("关闭成功");
      getList();
    });
};
</script>

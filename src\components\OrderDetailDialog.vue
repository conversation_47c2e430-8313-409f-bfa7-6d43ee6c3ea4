<template>
  <el-dialog
    title="订单详情"
    v-model="dialogVisible"
    width="60%"
    :before-close="handleClose"
    destroy-on-close
  >
    <div class="order-detail-container">
      <!-- 购买人信息 -->
      <div class="section">
        <h3 class="section-title">购买人信息</h3>
        <div class="info-row">
          <div class="info-item">
            <span class="label">购买人姓名：</span>
            <span class="value">{{orderDetail.bookName || '暂无数据' }}</span>
          </div>
          <div class="info-item" style="margin-left: 15px;">
            <span class="label">性别：</span>
            <span class="value">{{orderDetail.sexName || '暂无数据' }}</span>
          </div>
          <div class="info-item" >
            <span class="label">年龄：</span>
            <span class="value">{{orderDetail.age || '暂无数据' }}</span>
          </div>
          <div class="info-item" >
            <span class="label">购买人手机号：</span>
            <span class="value">{{orderDetail.telephone || '暂无数据' }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="label">身份证号：</span>
            <span class="value">{{orderDetail.certNum || '暂无数据' }}</span>
          </div>

        </div>
      </div>

      <!-- 商品信息 -->
      <div class="section">
        <h3 class="section-title">商品信息</h3>
        <div class="info-row" v-for="(item, index) in orderDetail.orderItemVOs" :key="index">
          <div class="info-item">
            <span class="label">商品名称：</span>
            <span class="value">{{item.goodsName || '暂无数据' }}</span>
          </div>
          <div class="info-item" style="margin-left: 40px;" >
            <span class="label">规格：</span>
            <span class="value">{{
              item.goodsSpecification && isValidJSON(item.goodsSpecification) 
                ? JSON.parse(item.goodsSpecification).spec || '暂无数据' 
                : '暂无数据'
            }}</span>
          </div>
        </div>
      </div>

      <!-- 订单信息 -->
      <div class="section">
        <h3 class="section-title">订单信息</h3>
        <div class="info-row">
          <div class="info-item">
            <span class="label">订单编号：</span>
            <span class="value">{{orderDetail.orderNo || '暂无数据' }}</span>
          </div>
          <div class="info-item" style="margin-left: 40px;">
            <span class="label">商品总价：</span>
            <span class="value">{{orderDetail.totalPrice || '0' }}</span>
          </div>
          <div class="info-item" style="margin-left: 40px;">
            <span class="label">商品总数：</span>
            <span class="value">{{orderDetail.orderItemVOs && orderDetail.orderItemVOs.length > 0 ? orderDetail.orderItemVOs[0].goodsCount : '0' }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item" >
            <span class="label">已核销：</span>
            <span class="value">{{orderDetail.orderItemVOs && orderDetail.orderItemVOs.length > 0 ? orderDetail.orderItemVOs[0].writeOffCount : '0'}}</span>
          </div>
          <div class="info-item" >
            <span class="label">待使用：</span>
            <span class="value">{{orderDetail.orderItemVOs && orderDetail.orderItemVOs.length > 0 ? orderDetail.orderItemVOs[0].goodsCount-orderDetail.orderItemVOs[0].writeOffCount-orderDetail.orderItemVOs[0].refundCount : '0' }}</span>
          </div>
          <div class="info-item" >
            <span class="label">已退款：</span>
            <span class="value">{{orderDetail.orderItemVOs && orderDetail.orderItemVOs.length > 0 ? orderDetail.orderItemVOs[0].refundCount : '0'}}</span>
          </div>
          <div class="info-item" >
            <span class="label">实付金额：</span>
            <span class="value">{{orderDetail.totalPrice || '0' }}</span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item" >
            <span class="label">支付方式：</span>
            <span class="value">{{getPayTypeText(orderDetail.payType) }}</span>
          </div>
          <div class="info-item" style="margin-left: 40px;">
            <span class="label">支付状态：</span>
            <span class="value">{{orderDetail.paystatusString || (orderDetail.payStatus === 1 ? '已支付' : '未支付') }}</span>
          </div>
            <div class="info-item"style="margin-left: 40px;" >
            <span class="label" >订单状态：</span>
            <span class="value">
              <template v-if="orderDetail.orderStatus === 0">待支付</template>
              <template v-else-if="orderDetail.orderStatus === 1">已支付</template>
              <template v-else-if="orderDetail.orderStatus === 2">配货完成</template>
              <template v-else-if="orderDetail.orderStatus === 3">出库成功</template>
              <template v-else-if="orderDetail.orderStatus === 4">交易成功</template>
              <template v-else-if="orderDetail.orderStatus === 5">核销成功</template>
              <template v-else-if="orderDetail.orderStatus === 6">核销后取消核销</template>
              <template v-else-if="orderDetail.orderStatus === -1">用户主动关闭</template>
              <template v-else-if="orderDetail.orderStatus === -2">系统超时关闭</template>
              <template v-else-if="orderDetail.orderStatus === -3">商家关闭</template>
              <template v-else>未知状态</template>
            </span>
          </div>
        </div>
        <div class="info-row">
          <div class="info-item">
            <span class="label">创建时间：</span>
            <span class="value">{{formatTimestamp(orderDetail.createTime) }}</span>
            <span class="label" style="margin-left: 40px;">支付时间：</span>
            <span class="value">{{formatTimestamp(orderDetail.payTime) }}</span>
          </div>
        </div>
        <!-- <div class="info-row">
          <div class="info-item">
            <span class="label">支付时间：</span>
            <span class="value">{{ formatTimestamp(orderDetail.payTime) }}</span>
          </div>
          <div class="info-item" v-if="orderDetail.refundResultTime">
            <span class="label">退款时间：</span>
            <span class="value">{{ formatTimestamp(orderDetail.refundResultTime) }}</span>
          </div>
        </div> -->
        <!-- <div class="info-row" v-if="orderDetail.refundTypeString">
          <div class="info-item">
            <span class="label">退款方式：</span>
            <span class="value">{{ orderDetail.refundTypeString }}</span>
          </div>
        </div> -->
      </div>
    </div>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { defineProps, defineEmits, ref, watch } from 'vue'
import axios from '@/utils/axios'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  orderId: {
    type: [Number, String],
    default: ''
  }
})

const emit = defineEmits(['update:visible'])

const dialogVisible = ref(false)
const orderDetail = ref({
  bookName: '',
  sexName: '',
  age: '',
  certNum: '',
  telephone: '',
  orderItemVOs: [],
  orderNo: '',
  totalPrice: '',
  goodsCount: '',
  actualPrice: '',
  payType: '',
  payStatus: '',
  paystatusString: '',
  orderStatus: '',
  orderStatusString: '',
  createTime: '',
  payTime: '',
  refundTypeString: '',
  refundTime: '',
  refundResultTime: '',
  payOrderNo: ''
})

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal && props.orderId) {
    getOrderDetail(props.orderId)
  }
})

// 监听dialogVisible变化，同步更新父组件的visible属性
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 获取订单详情
const getOrderDetail = (id) => {
  axios.get(`/orders/${id}`).then(res => {
    console.log('订单详情数据:', res)
    // 将后端返回的数据映射到我们需要的结构
    orderDetail.value = {
      bookName: res.bookName || '',
      sexName: res.sexName || '',
      age: res.age || '',
      certNum: res.certNum || '',
      telephone: res.telephone || '',
      orderItemVOs: res.orderItemVOs || [],
      orderNo: res.orderNo || '',
      totalPrice: res.totalPrice || 0,
      goodsCount: res.goodsCount || 0,
      actualPrice: res.actualPrice || 0,
      payType: res.payType || 0,
      payStatus: res.payStatus || 0,
      paystatusString: res.paystatusString || '',
      orderStatus: res.orderStatus || 0,
      orderStatusString: res.orderStatusString || null,
      createTime: res.createTime || '',
      payTime: res.payTime || '',
      refundTypeString: res.refundTypeString || '',
      refundTime: res.refundTime || '',
      refundResultTime: res.refundResultTime || '',
      payOrderNo: res.payOrderNo || ''
    }
  }).catch(error => {
    console.error('获取订单详情失败:', error)
  })
}

// 获取支付方式文本
const getPayTypeText = (payType) => {
  switch (payType) {
    case 1:
      return '支付宝支付'
    case 2:
      return '微信支付'
    case 0:
    return '无'
    default:
      return '未知'
  }
}



// 关闭弹窗
const handleClose = () => {
  dialogVisible.value = false
}

// 格式化时间戳或时间字符串
const formatTimestamp = (timestamp) => {
  if (!timestamp) return '暂无数据'

  // 如果已经是格式化好的字符串，直接返回
  if (typeof timestamp === 'string' && timestamp.includes('-')) {
    return timestamp
  }

  // 判断是否为数字字符串，如果是则转换为数字
  if (typeof timestamp === 'string' && !isNaN(timestamp)) {
    timestamp = parseInt(timestamp)
  }

  // 创建日期对象
  const date = new Date(timestamp)

  // 检查日期是否有效
  if (isNaN(date.getTime())) return '无效日期'

  // 格式化日期
  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  const seconds = String(date.getSeconds()).padStart(2, '0')

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
}

// 判断字符串是否为有效的JSON
const isValidJSON = (str) => {
  try {
    JSON.parse(str)
    return true
  } catch (e) {
    return false
  }
}
</script>

<style scoped>
.order-detail-container {
  padding: 0 20px;
}

.section {
  margin-bottom: 20px;
  border-bottom: 1px solid #EBEEF5;
  padding-bottom: 15px;
}

.section:last-child {
  border-bottom: none;
}

.section-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 15px;
  color: #303133;
}

.info-row {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 10px;
}

.info-item {
  display: flex;
  min-width: 20%;
  margin-bottom: 8px;
}

.label {
  color: #606266;
}

.value {
  color: #303133;
  word-break: break-all;
}

@media screen and (max-width: 768px) {
  .info-item {
    min-width: 100%;
  }
}
</style>




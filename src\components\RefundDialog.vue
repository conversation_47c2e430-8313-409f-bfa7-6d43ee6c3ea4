<template>
  <el-dialog
    v-model="dialogVisible"
    width="50%"
    :before-close="handleCancel"
    :show-header="false"
    destroy-on-close
  >
    <div class="refund-container">
      <h2 class="refund-title">退款</h2>
      <!-- <div class="refund-item">
        <span class="label">售后类型：</span>
        <el-select v-model="refundForm.serviceType" placeholder="请选择售后类型" :disabled="!isAdmin">
          <el-option label="退货退款" value="1"></el-option>
          <el-option label="仅退款" value="2"></el-option>
        </el-select>
      </div> -->

      <div class="refund-item">
        <span class="label">退款类型：</span>
        <el-radio-group v-model="refundForm.refundType">
          <el-radio label="1">整单退款</el-radio>
        </el-radio-group>
      </div>

      <div class="refund-item">
        <span class="label">预计退款金额：</span>
        <span class="amount">{{ orderDetail.remainRefundAmount }}元</span>
        <span class="total-price">(商品总价：{{ orderDetail.totalPrice || 0 }})</span>
      </div>

      <div class="refund-item" v-if="orderDetail.orderItemVOs && orderDetail.orderItemVOs.length > 0">
        <span class="refund-note">订单剩余：{{ getRemainingCount() }}份，具体退款金额以实际为准</span>
        <!-- <span class="remaining-count">{{ getRemainingCount() }}份</span>
        <span class="refund-note">具体退款金额以实际为准</span> -->
      </div>

      <!-- <div class="refund-note">
        具体退款金额以实际为准
      </div> -->
    </div>

    <template #footer>
      <span class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="submitting">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { defineProps, defineEmits, ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import axios from '@/utils/axios'

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  orderId: {
    type: [Number, String],
    default: ''
  },
  isAdmin: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['update:visible', 'refund-success'])

const dialogVisible = ref(false)
const submitting = ref(false)
const orderDetail = ref({})

const refundForm = reactive({
  serviceType: '2', // 默认仅退款
  refundType: '1', // 默认整单退款
  refundAmount: 0,
  orderId: ''
})

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal && props.orderId) {
    refundForm.orderId = props.orderId
    getOrderDetail(props.orderId)
  }
})

// 监听dialogVisible变化，同步更新父组件的visible属性
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 获取订单详情
const getOrderDetail = (id) => {
  axios.get(`/orders/${id}`).then(res => {
    orderDetail.value = res
    // 设置退款金额为订单实际支付金额
    refundForm.refundAmount = res.actualPrice || res.totalPrice || 0
  }).catch(error => {
    console.error('获取订单详情失败:', error)
    ElMessage.error('获取订单详情失败')
  })
}

// 取消退款
const handleCancel = () => {
  dialogVisible.value = false
}

// 获取订单剩余份数
const getRemainingCount = () => {
  if (!orderDetail.value || !orderDetail.value.orderItemVOs || orderDetail.value.orderItemVOs.length === 0) {
    return 0
  }

  const orderItem = orderDetail.value.orderItemVOs[0]
  const totalCount = orderItem.goodsCount || 0
  const usedCount = orderItem.writeOffCount || 0
  const refundCount = orderItem.refundCount || 0

  // 计算剩余可用份数 = 总数 - 已核销数 - 已退款数
  return totalCount - usedCount - refundCount
}

// 确认退款
const handleConfirm = () => {
  if (!orderDetail.value || !orderDetail.value.orderNo) {
    ElMessage.error('订单号不能为空')
    return
  }

  submitting.value = true

  // 根据API要求，只需要传递订单号作为参数
  const params = {
    orderNo: orderDetail.value.orderNo
  }

  // 调用订单退款接口，将订单号作为请求体发送
  axios.post('/orders/refund', params).then(() => {
    ElMessage.success('退款申请已提交')
    dialogVisible.value = false
    emit('refund-success')
  }).catch(error => {
    console.error('退款申请提交失败:', error)
    ElMessage.error('退款申请提交失败')
  }).finally(() => {
    submitting.value = false
  })
}


</script>

<style scoped>
.refund-container {
  padding: 20px;
}

.refund-title {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #EBEEF5;
}

.refund-item {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
}

.label {
  width: 120px;
  color: #606266;
  text-align: right;
  margin-right: 10px;
}

.amount {
  font-size: 18px;
  font-weight: bold;
  color: #f56c6c;
  margin-right: 10px;
}

.total-price {
  color: #909399;
  font-size: 14px;
}

.remaining-count {
  font-size: 16px;
  font-weight: bold;
  color: #409EFF;
  margin-right: 10px;
}

.refund-note {
  color: #909399;
  font-size: 14px;
  margin-left: 130px;
}
</style>

import axios from '@/utils/axios'

/**
 * 获取商品规格列表
 * @param {Object} params - 请求参数
 * @param {number} params.currPage - 页码
 * @param {number} params.pageSize - 每页数量
 * @returns {Promise} - 返回Promise对象
 */
export function getSpecificationList(params) {
  return axios.get('/showSpecifications', { params })
}

/**
 * 新增商品规格
 * @param {Object} data - 规格数据
 * @param {string} data.specName - 规格名称
 * @param {Object} data.specData - 规格和属性数据
 * @returns {Promise} - 返回Promise对象
 */
export function addSpecification(data) {
  return axios.post('/addSpecification', data)
}

/**
 * 更新商品规格
 * @param {Object} data - 规格数据
 * @param {string} data.specName - 规格名称
 * @param {Object} data.specData - 规格和属性数据
 * @returns {Promise} - 返回Promise对象
 */
export function updateSpecification(data) {
  return axios.post('/updateSpecification', data)
}

/**
 * 删除商品规格
 * @param {number} sequence - 规格序列号
 * @returns {Promise} - 返回Promise对象
 */
export function deleteSpecification(name) {
  return axios.get('/deleteByName', { params: { name } })
}

<template>
  <el-dialog
    :title="type == 'add' ? '添加商户' : (type == 'edit' ? '编辑库存' : '预览商户')"
    v-model="state.visible"
    width="50%"
  >
    <el-form :model="state.ruleForm" :rules="state.rules" ref="formRef" label-width="100px" class="good-form">
     
      <el-table
    :data="tableData"
    style="width: 100%;"
    border
    highlight-current-row
  >
    <!-- 图片列（按钮） -->
    <el-table-column label="图片" >
      <template #default="scope">
    <el-image
      :src="$filters.prefix(scope.row.goodsCarousel)"
      :preview-src-list="[scope.row.goodsCarousel]"
      style="width: 80px; height: 80px"
      fit="cover"
    />
  </template>
    </el-table-column>

    <!-- 售价列（数字编辑） -->
    <el-table-column label="售价（元）" prop="goodsSellingPrice" >
    
    </el-table-column>

    <!-- 成本价列（数字编辑） -->
    <el-table-column label="成本价（元）" prop="goodsCostPrice" >
      
    </el-table-column>

    <!-- 原价列（数字编辑） -->
    <el-table-column label="原价（元）" prop="goodsOriginalPrice" >
     
    </el-table-column>

    <!-- 库存列（数字编辑） -->
    <el-table-column label="库存(点击数字编辑)"  width="150" prop="goodsStockNum">
      <template #default="{ row, $index }">
        <el-input-number
        style="width: 100px;margin-left: 10px;" 
          v-if="activeEdit.index === $index && activeEdit.field === 'goodsStockNum'"
          v-model.number="row.goodsStockNum"
          :min="0"
          :step="1"
          size="small"
          @blur="saveEdit($index)"
        />
        <span v-else @click="startEdit($index, 'goodsStockNum')">
          {{ row.goodsStockNum || 0 }}
        </span>
      </template>
    </el-table-column>

    <!-- 库存预警列（开关编辑） -->
    <el-table-column label="库存预警" prop="goodsStockWarn">
      
    </el-table-column>

    <!-- 自定义规格列（文本编辑） -->
    <el-table-column label="重量(kg)" prop="goodsWeight" >
      
    </el-table-column>
    <el-table-column label="体积(平方)" prop="goodsVolume" >
      
    </el-table-column>
  </el-table>
     
    </el-form>
    <template v-if="type == 'preview' ? false:  true" #footer>
      <span class="dialog-footer">
        <el-button @click="state.visible = false">取 消</el-button>
        <el-button type="primary" @click="submitForm">确 定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup>
import { reactive, ref } from 'vue'
import axios from '@/utils/axios'
import { localGet, uploadImgServer } from '@/utils'
import { ElMessage } from 'element-plus'

const props = defineProps({
  type: String,
  reload: Function
})

const formRef = ref(null)
const state = reactive({
  uploadImgServer,
  token: localGet('token') || '',
  visible: false,
  tableData: [{}],
  ruleForm: {
    url: '',
    link: '',
    sort: ''
  },
  rules: {
    url: [
      { required: 'true', message: '商户logo不能为空', trigger: ['change'] }
    ],
    link: [
      { required: 'true', message: '商户名称不能为空', trigger: ['change'] }
    ],
    sort: [
      { required: 'true', message: '商户手机号不能为空', trigger: ['change'] }
    ]
  },
  id: ''
})

// const tableData = ref([
//   {
//     imgUrl: '',
//     price: 99.99,
//     costPrice: 50.00,
//     originalPrice: 129.99,
//     stock: 100,
//     stockAlert: true,
//     specs: '标准版'
//   }
// ]);

const tableData = ref([]);

// 当前编辑状态
const activeEdit = reactive({
  index: -1,
  field: ''
});

// 开始编辑
const startEdit = (index, field) => {
  activeEdit.index = index;
  activeEdit.field = field;
};

// 保存编辑
const saveEdit = (index) => {
  activeEdit.index = -1;
  activeEdit.field = '';
  // 这里可以添加数据保存逻辑
  // ElMessage.success(`第 ${index + 1} 行数据已更新`);
};


// 获取详情
const getDetail = (id) => {
  axios.get(`/carousels/${id}`).then(res => {
    state.ruleForm = {
      url: res.carouselUrl,
      link: res.redirectUrl,
      sort: res.carouselRank
    }
  })
}
const handleBeforeUpload = (file) => {
  const sufix = file.name.split('.')[1] || ''
  if (!['jpg', 'jpeg', 'png'].includes(sufix)) {
    ElMessage.error('请上传 jpg、jpeg、png 格式的图片')
    return false
  }
}
// 上传图片
const handleUrlSuccess = (val) => {
  state.ruleForm.url = val.data || ''
}
// 开启弹窗
const open = (row) => {
  state.visible = true
  if (row) {
    console.log(row,'row')
    // Object.assign(state.tableData, row);
    state.tableData.value  = [row]; 
    tableData.value = [row]; 
    // state.tableData = row.value
    console.log(state.tableData,'state.tableData')
    console.log(tableData,'tableData')
    // state.id = id
    // getDetail(id)
  } else {
    state.ruleForm = {
      url: '',
      link: '',
      sort: ''
    }
  }
}
// 关闭弹窗
const close = () => {
  state.visible = false
}
const submitForm = () => {
  console.log(formRef.value.validate)
  formRef.value.validate((valid) => {
    if (valid) {
      if (props.type == 'add') {
        axios.post('/carousels', {
          carouselUrl: state.ruleForm.url,
          redirectUrl: state.ruleForm.link,
          carouselRank: state.ruleForm.sort
        }).then(() => {
          ElMessage.success('添加成功')
          state.visible = false
          if (props.reload) props.reload()
        })
      } else {
        console.log(tableData.value[0].goodsStockNum,'goodsStockNum')
        axios.put('/goods', {
          goodsId:tableData.value[0].goodsId,
          goodsName:tableData.value[0].goodsName,
          goodsCategoryId:tableData.value[0].goodsCategoryId,
          goodsBelongOrganId:tableData.value[0].goodsBelongOrganId,
          goodsCoverImg:tableData.value[0].goodsCoverImg,
          goodsCarousel:tableData.value[0].goodsCarousel,
          goodsSellingPrice:tableData.value[0].goodsSellingPrice,
          goodsCostPrice:tableData.value[0].goodsCostPrice,
          goodsOriginalPrice:tableData.value[0].goodsOriginalPrice,
          goodsStockWarn:tableData.value[0].goodsStockWarn,
          goodsDetailContent:tableData.value[0].goodsDetailContent,
          goodsSellStatus:tableData.value[0].goodsSellStatus,
          goodsBelongMerchantId:tableData.value[0].goodsBelongMerchantId,
          goodsBelongType:tableData.value[0].goodsBelongType,
          goodsType:tableData.value[0].goodsType,
          goodsStockNum: tableData.value[0].goodsStockNum,
          // carouselUrl: state.ruleForm.url,
          // redirectUrl: state.ruleForm.link,
          // carouselRank: state.ruleForm.sort
        }).then(() => {
          ElMessage.success('修改成功')
          state.visible = false
          if (props.reload) props.reload()
        })
      }
    }
  })
}
defineExpose({ open, close })
</script>

<style scoped>
  .avatar-uploader {
    width: 100px;
    height: 100px;
    color: #ddd;
    font-size: 30px;
  }
  .avatar-uploader >>> .el-upload {
    width: 100%;
    text-align: center;
  }
  .avatar-uploader-icon {
    display: block;
    width: 100%;
    height: 100%;
    border: 1px solid #e9e9e9;
    padding: 32px 17px;
  }
</style>
import axios from './axios'
import { setUserMenus, getUserMenus, clearUserMenus, localRemove } from './index' // 确保导入 localRemove
import {componentMap} from '@/router'


/**
 * 获取用户菜单权限
 * @returns {Promise<Array>} 用户菜单数据
 */
export async function fetchUserMenus() {
    try {
        const response = await axios.get('/menus');
        if (response.resultCode === 419) {
            console.warn('管理员登录过期！请重新登录！');
            clearUserPermissions();
            router.push('/login');
            return null;
        }
        console.log(response,'response+')
        const menus = response || [];
        if(!menus){
            return null;
        }

        // 存储到本地存储
        setUserMenus(menus);
        return menus;
    } catch (error) {
        console.error('获取用户菜单权限失败:', error);
        return null;
    }
}

/**
 * 检查用户是否有访问某个路由的权限
 * @param {string} routePath 路由路径
 * @returns {boolean} 是否有权限
 */
export function hasRoutePermission(routePath) {
    const userMenus = getUserMenus()
    
    // 递归检查菜单权限
    function checkPermission(menus, path) {
        for (const menu of menus) {
            // 检查当前菜单项
            if (menu.path === path || menu.name === path) {
                return true
            }
            
            // 递归检查子菜单
            if (menu.children && menu.children.length > 0) {
                if (checkPermission(menu.children, path)) {
                    return true
                }
            }
        }
        return false
    }
    
    return checkPermission(userMenus, routePath)
}

/**
 * 将后端菜单数据转换为前端路由配置
 * @param {Array} menus 后端菜单数据
 * @returns {Array} 前端路由配置
 */
export function transformMenusToRoutes(menus) {
    const routes = []
    
    // 路由组件映射表
    // const componentMap = {
    //     // 'Layout': () => import('@/components/DynamicMenu.vue'),
    //     'login': () => import('@/views/Login.vue'),
    //     'goods': () => import('@/views/GoodNew.vue'),
    //     'category': () => import('@/views/Category.vue'),
    //     'supplier': () => import('@/views/SupplierList.vue'),
    //     'specification': () => import('@/views/Specification.vue'),
    //     'add': () => import('@/views/AddGoodSpec.vue'),
    //     'order': () => import('@/views/Order.vue'),
    //     'refundOrders': () => import('@/views/refundOrders.vue'),
    //     'merchants': () => import('@/views/Guest.vue'),
    //     'account': () => import('@/views/Account.vue'),
    //     'servicePackSetting': () => import('@/views/fwbSetting.vue'),
    //     'servicePackOrders': () => import('@/views/fwbOrders.vue'),
    //     'servicePackRefund': () => import('@/views/fwbRefundOrders.vue'),

    //     // 可以根据需要添加更多组件映射
    // }
    
    function transformMenu(menu) {
        const route = {
            path: menu.path,
            name: menu.name,
            component: componentMap[menu.name]||null,
            // || (() => import(`@/views/${menu.component}.vue`))
            meta: {
                title: menu.title,
                icon: menu.icon,
                administrator: menu.administrator,
                id: menu.id
            }
        }
        
        // 处理子路由
        if (menu.children && menu.children.length > 0) {
            route.children = menu.children.map(child => transformMenu(child))
        }
        
        return route
    }
    menus.forEach(menu => {
        routes.push(transformMenu(menu))
    })

        console.log(routes)
    return routes
}

/**
 * 清除用户权限数据
 */
export function clearUserPermissions() {
    clearUserMenus() // 清除菜单
    localRemove('userInfo') // 清除用户信息
    // 清除其他可能存在的用户特定权限数据，例如 token 也可以在这里统一管理，但通常在登出逻辑中处理
    // localRemove('token')
    console.log('用户权限数据已清除')
}

/**
 * 过滤菜单数据，只保留用户有权限的菜单
 * @param {Array} menus 菜单数据
 * @param {boolean} isAdmin 是否是管理员
 * @returns {Array} 过滤后的菜单数据
 */
export function filterMenusByPermission(menus, isAdmin = false) {
    return menus.filter(menu => {
        // 如果是管理员，显示所有管理员菜单
        if (isAdmin && menu.administrator) {
            return true
        }
        
        // 非管理员只显示非管理员菜单
        if (!isAdmin && !menu.administrator) {
            return true
        }
        
        return false
    }).map(menu => {
        // 递归过滤子菜单
        if (menu.children && menu.children.length > 0) {
            menu.children = filterMenusByPermission(menu.children, isAdmin)
        }
        return menu
    })
}

<template>
  <el-card class="order-container">
    <template #header>
      <div class="header">
        <el-input
          style="width: 200px; margin-right: 10px"
          placeholder="请输入用户手机号"
          v-model="state.phoneNo"
          clearable
        />
        <el-input
          style="width: 200px; margin-right: 10px"
          placeholder="请输入订单号"
          v-model="state.orderNo"
          clearable
        />

        <el-input
          style="width: 200px; margin-right: 10px"
          placeholder="请输入退款单号"
          v-model="state.refundSeq"
          clearable
        />
        <el-config-provider :locale="state.locale">
          <el-date-picker
            v-model="state.createTime"
            type="datetime"
            placeholder="请选择时间"
            format="YYYY-MM-DD HH:mm:ss"
            style="margin-right: 10px"
          />
        </el-config-provider>

        <el-select
          v-model="state.belong"
          placeholder="请选择商品归属"
          style="width: 150px; margin-right: 10px"
        >
          <el-option
            v-for="item in state.typeList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          />
        </el-select>

        <el-select
          v-model="state.organId"
          style="width: 200px; margin-right: 10px"
          placeholder="请选择商户"
        >
          <el-option
            v-for="item in state.userList"
            :key="item.merchantId"
            :label="item.merchantName"
            :value="item.merchantId"
          />
        </el-select>

        <!-- <el-button type="primary" size="small" icon="el-icon-edit">修改订单</el-button> -->
        <el-button type="primary" @click="handleOption()" style="margin-right: 10px"
          >搜索</el-button
        >
        <el-button type="warning" @click="clearSearch()">重置</el-button>
        <!-- <el-button
                    type="primary"
                    :icon="HomeFilled"
                    @click="handleConfig()"
                    >配货完成</el-button
                >
                <el-button
                    type="primary"
                    :icon="HomeFilled"
                    @click="handleSend()"
                    >出库</el-button
                >
                <el-button type="danger" :icon="Delete" @click="handleClose()"
                    >关闭订单</el-button
                > -->
      </div>
    </template>
    <!-- @selection-change="handleSelectionChange" -->
    <el-table
      :load="state.loading"
      :data="state.tableData"
      tooltip-effect="dark"
      style="width: 100%"
    >
      <!-- <el-table-column type="selection" width="55"> </el-table-column> -->
      <el-table-column prop="orderNo" label="订单编号"> </el-table-column>
      <el-table-column prop="refundSeq" label="退款单号"> </el-table-column>
      <el-table-column prop="merchantName" label="商户名称"> </el-table-column>
      <el-table-column prop="phoneNo" label="用户手机号"> </el-table-column>
      <el-table-column prop="refundAmount" label="退款金额">
        <template #default="scope">
          <span>{{ scope.row.refundAmount + "元" }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="serviceType" label="售后类型"> </el-table-column>
      <el-table-column prop="returnType" label="退货类型"> </el-table-column>
      <el-table-column prop="refundStatus" label="退款状态"> </el-table-column>
      <el-table-column prop="createTime" label="创建时间"> </el-table-column>
      <!-- <el-table-column prop="orderStatus" label="订单状态">
                <template #default="scope">
                    <span>{{ $filters.orderMap(scope.row.orderStatus) }}</span>
                </template>
            </el-table-column>
            <el-table-column prop="payType" label="支付方式">
                <template #default="scope">
                    <span v-if="scope.row.payType == 1">微信支付</span>
                    <span v-else-if="scope.row.payType == 2">支付宝支付</span>
                    <span v-else>未知</span>
                </template>
            </el-table-column>
            <el-table-column prop="createTime" label="创建时间">
            </el-table-column> -->
      <el-table-column label="操作">
        <template #default="scope">
          <el-button type="primary" @click="showDetail(scope.row)">详情</el-button>
          <!-- <el-popconfirm
                        v-if="scope.row.orderStatus == 1"
                        title="确定配货完成吗？"
                        @confirm="handleConfig(scope.row.orderId)"
                        confirm-button-text="确定"
                        cancel-button-text="取消"
                    >
                        <template #reference>
                            <a style="cursor: pointer; margin-right: 10px"
                                >配货完成</a
                            >
                        </template>
                    </el-popconfirm>
                    <el-popconfirm
                        v-if="scope.row.orderStatus == 2"
                        title="确定出库吗？"
                        @confirm="handleSend(scope.row.orderId)"
                        confirm-button-text="确定"
                        cancel-button-text="取消"
                    >
                        <template #reference>
                            <a style="cursor: pointer; margin-right: 10px"
                                >出库</a
                            >
                        </template>
                    </el-popconfirm>
                    <el-popconfirm
                        v-if="
                            !(
                                scope.row.orderStatus == 4 ||
                                scope.row.orderStatus < 0
                            )
                        "
                        title="确定关闭订单吗？"
                        @confirm="handleClose(scope.row.orderId)"
                        confirm-button-text="确定"
                        cancel-button-text="取消"
                    >
                        <template #reference>
                            <a style="cursor: pointer; margin-right: 10px"
                                >关闭订单</a
                            >
                        </template>
                    </el-popconfirm>
                    <router-link
                        :to="{
                            path: '/order_detail',
                            query: { id: scope.row.orderId },
                        }"
                        >订单详情</router-link
                    > -->
        </template>
      </el-table-column>
    </el-table>
    <el-pagination
      background
      layout="prev, pager, next"
      :total="state.total"
      :page-size="state.pageSize"
      :current-page="state.currentPage"
      @current-change="changePage"
    />
    <el-dialog v-model="state.dialogVisible" title="退款详情" width="80%">
      <h1>退款商品</h1>
      <div v-for="(it, index) in state.detail.orderItemVOs" :key="index">
        <div
          style="
            display: flex;
            align-content: center;
            justify-content: flex-start;
            align-items: center;
          "
        >
          <div
            style="
              width: 100px;
              height: 100px;
              margin-right: 18px;
              border: 1px solid gray;
              display: flex;
            "
          >
            <img
              :src="it.goodsCoverImg"
              style="max-width: 100px; max-height: 100px; margin: auto"
            />
          </div>

          <div>
            <p style="color: #333">{{ it.goodsName }}</p>
            <p>
              规格：{{ getSpec(it.goodsSpecification) }} &nbsp;&nbsp;&nbsp;&nbsp; 售价：{{
                it.sellingPrice
              }}元
            </p>
          </div>
        </div>
        <h1>退款明细</h1>
        <div>
          退款数量：{{ it.refundCount }} &nbsp;&nbsp;&nbsp;&nbsp; 购买数量：{{
            it.goodsCount
          }}&nbsp;&nbsp;&nbsp;&nbsp; 预计退款方式：原支付返还 &nbsp;&nbsp;&nbsp;&nbsp;
          退款金额：{{ it.refundAmount }}元
        </div>
      </div>

      <h1>退款流程信息</h1>
      <el-timeline
        ><el-timeline-item
          v-for="(it, index) in state.detail.refundLogVos"
          :key="index"
          :timestamp="it.operateTime"
        >
          <div>
            <!-- {{ it.operateTime }} -->
            {{ it.userTypeBody }}
            {{ it.refundStatusType }}
            {{ it.reason }}
          </div>
        </el-timeline-item>
      </el-timeline>
    </el-dialog>
  </el-card>
</template>

<script setup>
import { onMounted, reactive } from "vue";
import { ElMessage } from "element-plus";
import zhCn from "element-plus/es/locale/lang/zh-cn";
import { HomeFilled, Delete } from "@element-plus/icons-vue";
import axios from "@/utils/axios";

const state = reactive({
  dialogVisible: false,
  detail: {},
  createTime: "",
  locale: zhCn,
  loading: false,
  organId: "",
  belong: "",
  typeList: [
    { id: "1", name: "自营" },
    { id: "2", name: "第三方" },
  ],
  userList: [], //商户列表
  tableData: [], // 数据列表
  multipleSelection: [], // 选中项
  phoneNo: "",
  refundSeq: "",
  total: 0, // 总条数
  currentPage: 1, // 当前页
  pageSize: 10, // 分页大小
  orderNo: "", // 订单号
  orderStatus: "", // 订单状态
});
const showDetail = (row) => {
  // todo
  console.log("eeee", row);

  // /orders/{orderId}
  state.loading = true;
  axios.get("/orders/" + row.orderId, null).then((res) => {
    console.log(res);
    state.dialogVisible = true;
    state.detail = res;

    state.loading = false;
  });
};
// 初始化获取订单列表
onMounted(() => {
  getOrderList();
  getSysUsersList();
});
const getSpec = (str) => {
  return JSON.parse(str).spec;
};
// 获取列表方法
const getOrderList = () => {
  state.loading = true;
  axios
    .post("/refundOrders", {
      pageNumber: state.currentPage,
      pageSize: state.pageSize,
      orderNo: state.orderNo,
      createTime: state.createTime,
      merchantId: state.organId,
      phoneNo: state.phoneNo,
      refundSeq: state.refundSeq,
      belong: state.belong,
    })
    .then((res) => {
      state.tableData = res.list;
      state.total = res.totalCount;
      state.currentPage = res.currPage;
      state.loading = false;
    });
};
// 获取商户列表方法
const getSysUsersList = () => {
  state.loading = true;
  axios
    .get("/merchants", {
      params: {
        pageNumber: 1,
        pageSize: 999999,
      },
    })
    .then((res) => {
      state.userList = res.list;
      // state.loading = false;
    });
};
// 触发过滤项方法
const handleOption = () => {
  state.currentPage = 1;
  getOrderList();
};
const clearSearch = () => {
  state.currentPage = 1;
  state.currentPage = 1;
  state.orderNo = "";
  state.createTime = "";
  state.organId = "";
  state.phoneNo = "";
  state.refundSeq = "";
  state.belong = "";
  getOrderList();
};

// checkbox 选择项
const handleSelectionChange = (val) => {
  state.multipleSelection = val;
};
// 翻页方法
const changePage = (val) => {
  state.currentPage = val;
  getOrderList();
};
// 配货方法
const handleConfig = (id) => {
  let params;
  // 当个配置
  if (id) {
    params = [id];
  } else {
    if (!state.multipleSelection.length) {
      console.log("state.multipleSelection", state.multipleSelection.length);
      ElMessage.error("请选择项");
      return;
    }
    // 多选配置
    params = state.multipleSelection.map((i) => i.orderId);
  }
  axios
    .put("/orders/checkDone", {
      ids: params,
    })
    .then(() => {
      ElMessage.success("配货成功");
      getOrderList();
    });
};
// 出库方法
const handleSend = (id) => {
  let params;
  if (id) {
    params = [id];
  } else {
    if (!state.multipleSelection.length) {
      ElMessage.error("请选择项");
      return;
    }
    params = state.multipleSelection.map((i) => i.orderId);
  }
  axios
    .put("/orders/checkOut", {
      ids: params,
    })
    .then(() => {
      ElMessage.success("出库成功");
      getOrderList();
    });
};
// 关闭订单方法
const handleClose = (id) => {
  let params;
  if (id) {
    params = [id];
  } else {
    if (!state.multipleSelection.length) {
      ElMessage.error("请选择项");
      return;
    }
    params = state.multipleSelection.map((i) => i.orderId);
  }
  axios
    .put("/orders/close", {
      ids: params,
    })
    .then(() => {
      ElMessage.success("关闭成功");
      getOrderList();
    });
};
</script>
